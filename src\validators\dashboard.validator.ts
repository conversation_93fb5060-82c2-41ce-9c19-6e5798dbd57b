import { Segments, Joi, celebrate } from "celebrate";
export default {
  addDashboard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_name: Joi.string().required(),
        dashboard_filter: Joi.string().required(),
        model_list: Joi.array().items(Joi.object()).unique().min(1).required(),
      }),
    }),
  updateDashboard: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_name: Joi.string().required(),
        dashboard_filter: Joi.string().required(),
        model_list: Joi.array().items(Joi.object()).unique().min(1).required(),
      }),
    }),
  getDashboardWidgets: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        dashboard_type: Joi.string()
          .valid("user", "sales", "setup")
          .required()
          .messages({
            "any.only": "Dashboard type must be one of: user, sales, setup",
            "any.required": "Dashboard type is required",
          }),
        // Optional filters
        branch_ids: Joi.alternatives()
          .try(Joi.number(), Joi.string(), Joi.array().items(Joi.number()))
          .optional(),
        widget_type: Joi.string().valid("counts", "charts", "all").optional(),
        chart_type: Joi.string()
          .valid("line", "bar", "pie", "multi_line", "meter", "area")
          .optional(),
        date_filter: Joi.string().optional(),
        filter_time_period: Joi.string().optional(),
        payment_type_category_id: Joi.string().optional(),
        year: Joi.string().optional(),
      }),
    }),
};
