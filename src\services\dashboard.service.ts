// Core Sequelize imports
import { sequelize } from "../models";
import { Op, QueryTypes } from "sequelize";

// Model imports (organized alphabetically)
import { Branch, branch_status } from "../models/Branch";
import { ChangeRequest } from "../models/ChangeRequest";
import { Department } from "../models/Department";
import { HolidayPolicy } from "../models/HolidayPolicy";
import { HolidayType } from "../models/HolidayType";

import { LeaveAccuralPolicy } from "../models/LeaveAccuralPolicy";
import { LeaveTypeModel } from "../models/LeaveType";
import { Role as MORole } from "../models/MORole";
import { Resignation } from "../models/Resignation";
import { User, user_status } from "../models/User";
import {
  UserEmploymentContract,
  contract_status,
} from "../models/UserEmployementContract";
import { UserMeta } from "../models/UserMeta";
import { UserRequest, request_status } from "../models/UserRequest";
import { DsrDetail, dsr_detail_status } from "../models/DsrDetail";
import { DsrItem, dsr_item_status } from "../models/DsrItem";
import { PaymentType, payment_type_usage } from "../models/PaymentType";
import { PaymentTypeCategory } from "../models/PaymentTypeCategory";
import { getGeneralSettingObj, getTimePeriodConfig } from "../helper/common";
import moment from "moment";
import { forecast_category_type } from "../models/ForecastBugdetData";

// Configure moment.js for consistent behavior
moment.locale("en");

// ============================================================================
// DASHBOARD SERVICE
// ============================================================================
/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets and statistics
 * Organized into sections: Enums, Interfaces, Statistics Functions, Main Functions
 */

// ============================================================================
// ENUMS & CONSTANTS
// ============================================================================

// Enums for chart identification
enum ChartType {
  ONBOARDING_PIE = "onboarding_pie",
  ONBOARDING_BAR = "onboarding_bar",
  CONTRACT_GAUGE = "contract_gauge",
  LEAVE_COMPARISON = "leave_comparison",
  SALES_COMPARISON = "sales_comparison",
}

// ============================================================================
// INTERFACES & TYPES
// ============================================================================

// Types for Recharts compatibility
interface RechartsDataPoint {
  name: string;
  value: number;
  [key: string]: any; // Allow additional properties for multi-series data
}

interface RechartsLineBarData {
  data: RechartsDataPoint[];
  xAxisKey: string;
  yAxisKey: string;
  series: {
    dataKey: string;
    name: string;
    color: string;
    type?: "line" | "bar";
  }[];
}

interface RechartsPieData {
  data: RechartsDataPoint[];
  nameKey: string;
  valueKey: string;
  colors: string[];
}

interface CommonChartResponse {
  success: boolean;
  chartType: "line" | "bar" | "pie" | "gauge" | "multi_line" | "meter" | "area";
  title: string;
  data: RechartsLineBarData | RechartsPieData | any; // Added 'any' for gauge data
  metadata?: {
    totalRecords: number;
    dateRange?: string;
    timelinePeriod?: string;
    branchCount?: number;
    filters?: any;
    lastUpdated: string;
    [key: string]: any; // Allow additional metadata for gauge charts
  };
}

// Chart configuration interface
interface ChartConfig {
  type: ChartType;
  widgetType: string;
  title: string;
  chartType: "line" | "bar" | "pie" | "gauge" | "multi_line" | "meter" | "area";
  category: "chart";
  description: string;
  hasFilters: boolean;
  dataFetcher: (
    organizationId: string,
    filters?: FilterOptions
  ) => Promise<CommonChartResponse>;
}

interface FilterOptions {
  branch_ids?: string | number | number[]; // Single key for both single and multiple branch IDs
  widgetType?: "counts" | "charts" | "all";
  chartType?:
    | "line"
    | "bar"
    | "pie"
    | "gauge"
    | "multi_line"
    | "meter"
    | "area";
  activity_action?: "created" | "updated" | "login" | "logout";
  notification_status?: "read" | "pending";
}

// Cache for frequently accessed data (in production, use Redis or similar)
const dataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch if expired
 */
const getCachedData = async <T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = CACHE_TTL
): Promise<T> => {
  const cached = dataCache.get(key);
  const now = Date.now();

  if (cached && now - cached.timestamp < ttl) {
    return cached.data as T;
  }

  const data = await fetchFn();
  dataCache.set(key, { data, timestamp: now });
  return data;
};

/**
 * Validate required parameters
 */
const validateParameters = (organizationId: string, userId: number): void => {
  if (!organizationId || typeof organizationId !== "string") {
    throw new Error("Invalid organizationId: must be a non-empty string");
  }
  if (!userId || typeof userId !== "number" || userId <= 0) {
    throw new Error("Invalid userId: must be a positive number");
  }
};

/**
 * Generate date range for the last N months using Moment.js
 */
const generateMonthRange = (
  months: number
): { start: Date; monthKeys: string[]; monthLabels: string[] } => {
  const start = moment().subtract(months, "months").startOf("month").toDate();

  const monthKeys: string[] = [];
  const monthLabels: string[] = [];

  for (let i = months - 1; i >= 0; i--) {
    const monthMoment = moment().subtract(i, "months");
    monthKeys.push(monthMoment.format("YYYY-MM"));
    monthLabels.push(monthMoment.format("MMM YYYY"));
  }

  return { start, monthKeys, monthLabels };
};

/**
 * Utility functions for consistent date handling with Moment.js
 */
const DateUtils = {
  /**
   * Validate and format date to YYYY-MM format for month grouping
   */
  toMonthKey: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to toMonthKey: ${date}`);
      return moment().format("YYYY-MM"); // Return current month as fallback
    }
    return momentDate.format("YYYY-MM");
  },

  /**
   * Format date to human-readable month label
   */
  toMonthLabel: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to toMonthLabel: ${date}`);
      return moment().format("MMM YYYY"); // Return current month as fallback
    }
    return momentDate.format("MMM YYYY");
  },

  /**
   * Check if date is within the last N months
   */
  isWithinLastMonths: (
    date: string | Date | moment.Moment,
    months: number
  ): boolean => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to isWithinLastMonths: ${date}`);
      return false;
    }
    const cutoffDate = moment().subtract(months, "months").startOf("month");
    return momentDate.isAfter(cutoffDate);
  },

  /**
   * Get start of month for a given date
   */
  startOfMonth: (date: string | Date | moment.Moment): Date => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to startOfMonth: ${date}`);
      return moment().startOf("month").toDate(); // Return current month start as fallback
    }
    return momentDate.startOf("month").toDate();
  },

  /**
   * Format date for display with validation
   */
  formatForDisplay: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to formatForDisplay: ${date}`);
      return moment().format("YYYY-MM-DD"); // Return current date as fallback
    }
    return momentDate.format("YYYY-MM-DD");
  },

  /**
   * Get relative time from now (e.g., "2 days ago")
   */
  fromNow: (date: string | Date | moment.Moment): string => {
    const momentDate = moment(date);
    if (!momentDate.isValid()) {
      console.warn(`Invalid date provided to fromNow: ${date}`);
      return "Invalid date";
    }
    return momentDate.fromNow();
  },

  /**
   * Check if date is valid
   */
  isValid: (date: string | Date | moment.Moment): boolean => {
    return moment(date).isValid();
  },

  /**
   * Get current timestamp in ISO format
   */
  now: (): string => {
    return moment().toISOString();
  },
};

/**
 * Get onboarding pipeline status data for pie chart (Recharts format)
 * Based on getOnboardingPipelineBarData but converted to pie chart format
 * Shows distribution of Completed and Verified users across all branches
 * Supports branch filtering to show data for specific branch only
 */
const getOnboardingPipelinePieData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `onboarding_pie_${organizationId}_${filters?.branch_ids || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clauses for branches and users
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      const userWhere: any = {
        organization_id: organizationId,
      };

      // Apply branch filtering if specified
      if (filters?.branch_ids) {
        // Handle both single and multiple branch IDs
        const branchArray = Array.isArray(filters.branch_ids)
          ? filters.branch_ids
          : [filters.branch_ids];

        if (branchArray.length === 1) {
          branchWhere.id = branchArray[0];
          userWhere.branch_id = branchArray[0];
        } else {
          branchWhere.id = { [Op.in]: branchArray };
          userWhere.branch_id = { [Op.in]: branchArray };
        }
      }

      // Execute queries in parallel for better performance
      const [branches, users] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        User.findAll({
          where: userWhere,
          attributes: ["id", "branch_id", "user_status"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "pie",
          title: "Onboarding Pipeline Distribution",
          data: {
            data: [],
            nameKey: "name",
            valueKey: "value",
            colors: ["#10B981", "#8B5CF6"],
          } as RechartsPieData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Initialize status counters (only Completed and Verified as per line chart logic)
      const statusCounts = {
        Completed: 0,
        Verified: 0,
      };

      // Aggregate user counts by status (only Completed and Verified)
      users.forEach((user: any) => {
        switch (user.user_status) {
          case user_status.COMPLETED:
            statusCounts.Completed++;
            break;
          case user_status.VERIFIED:
            statusCounts.Verified++;
            break;
          // Skip pending, active, and ongoing statuses as per line chart logic
        }
      });

      // Define colors for each status
      const statusColors = {
        Completed: "#10B981", // Green
        Verified: "#8B5CF6", // Purple
      };

      // Format data for Recharts pie chart
      const chartData: RechartsDataPoint[] = Object.entries(statusCounts)
        .filter(([, count]) => count > 0) // Only include statuses with data
        .map(([status, count]) => ({
          name: status,
          value: count,
          status: status,
          count: count,
          color: statusColors[status as keyof typeof statusColors],
        }));

      const totalRecords = chartData.reduce((sum, item) => sum + item.value, 0);

      // Convert statusColors object to array for RechartsPieData compatibility
      const colorsArray = Object.values(statusColors);

      return {
        success: true,
        chartType: "pie",
        title: "Onboarding Pipeline Distribution",
        data: {
          data: chartData,
          nameKey: "name",
          valueKey: "value",
          colors: colorsArray,
        } as RechartsPieData,
        metadata: {
          totalRecords,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getOnboardingPipelinePieData:", error);
      return {
        success: false,
        chartType: "pie",
        title: "Onboarding Pipeline Distribution",
        data: {
          data: [],
          nameKey: "name",
          valueKey: "value",
          colors: ["#10B981", "#8B5CF6"],
        } as RechartsPieData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get onboarding pipeline status data for bar chart (Recharts format)
 * Based on getOnboardingPipelineLineData but converted to bar chart format
 * X-axis: Branches, Y-axis: User counts
 * 2 data series: Completed, Verified (matching line chart logic)
 */
const getOnboardingPipelineBarData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `onboarding_bar_${organizationId}_all`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clauses for branches and users
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      const userWhere: any = {
        organization_id: organizationId,
      };

      // Execute queries in parallel for better performance
      const [branches, users] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name"],
          raw: true,
        }),
        User.findAll({
          where: userWhere,
          attributes: ["id", "branch_id", "user_status"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "bar",
          title: "Onboarding Pipeline Status by Branch",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [
              {
                dataKey: "completed",
                name: "Completed",
                color: "#10B981",
                type: "bar",
              },
              {
                dataKey: "verified",
                name: "Verified",
                color: "#8B5CF6",
                type: "bar",
              },
            ],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Initialize branch data structure (only completed and verified as per line chart)
      const branchData = new Map<
        number,
        {
          name: string;
          completed: number;
          verified: number;
        }
      >();

      // Initialize all branches with zero counts
      branches.forEach((branch: any) => {
        branchData.set(branch.id, {
          name: branch.branch_name,
          completed: 0,
          verified: 0,
        });
      });

      // Aggregate user counts by branch and status (only completed and verified)
      users.forEach((user: any) => {
        const branchInfo = branchData.get(user.branch_id);
        if (branchInfo) {
          switch (user.user_status) {
            case user_status.COMPLETED:
              branchInfo.completed++;
              break;
            case user_status.VERIFIED:
              branchInfo.verified++;
              break;
            // Skip pending, active, and ongoing statuses as per line chart logic
          }
        }
      });

      // Transform to Recharts format
      const chartData: RechartsDataPoint[] = Array.from(
        branchData.values()
      ).map((branch) => ({
        name: branch.name,
        value: branch.completed + branch.verified, // Total for reference
        completed: branch.completed,
        verified: branch.verified,
      }));

      const totalRecords = users.length;

      return {
        success: true,
        chartType: "bar",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "bar",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "bar",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getOnboardingPipelineBarData:", error);
      return {
        success: false,
        chartType: "bar",
        title: "Onboarding Pipeline Status by Branch",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [
            {
              dataKey: "completed",
              name: "Completed",
              color: "#10B981",
              type: "bar",
            },
            {
              dataKey: "verified",
              name: "Verified",
              color: "#8B5CF6",
              type: "bar",
            },
          ],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get user contract status data for gauge chart (Recharts format)
 * Based on getUserContractPieData but converted to gauge/meter chart format
 * Shows contract health metrics: percentage of healthy vs problematic contracts
 * Healthy: Confirmed + Probation
 * Problematic: Expired + Expiry Soon + Pending + Awaiting Signature
 */
const getUserContractGaugeData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `contract_gauge_${organizationId}_${filters?.branch_ids || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clause for User table
      const userWhere: any = {
        organization_id: organizationId,
      };

      if (filters?.branch_ids) {
        // Handle both single and multiple branch IDs
        const branchArray = Array.isArray(filters.branch_ids)
          ? filters.branch_ids
          : [filters.branch_ids];

        if (branchArray.length === 1) {
          userWhere.branch_id = branchArray[0];
        } else {
          userWhere.branch_id = { [Op.in]: branchArray };
        }
      }

      // Get all users with their contracts and meta data for probation calculation
      const usersWithContracts = await User.findAll({
        include: [
          {
            model: UserEmploymentContract,
            as: "user_contract",
            attributes: ["contract_status", "expire_date", "is_confirm_sign"],
            where: {
              contract_status: contract_status.ACTIVE,
            },
            required: true,
          },
          {
            model: UserMeta,
            as: "user_meta",
            attributes: ["probation_length"],
            required: false,
          },
        ],
        attributes: ["id", "user_joining_date"],
        where: userWhere,
        raw: true,
        nest: true,
      });

      // Initialize status counters
      const statusCounts = {
        Expired: 0,
        "Expiry Soon": 0,
        Probation: 0,
        Confirmed: 0,
        Pending: 0,
        "Awaiting Signature": 0,
      };

      // Process each user and categorize based on frontend logic
      usersWithContracts.forEach((user: any) => {
        const currentDate = moment().startOf("day");
        const expireDate = moment(user.user_contract.expire_date).startOf(
          "day"
        );
        const daysDifference = expireDate.diff(currentDate, "days");

        // Determine probation status
        const joiningDate = moment(user.user_joining_date);
        const probationLength = user.user_meta?.probation_length || 0;
        const probationEndDate = joiningDate
          .clone()
          .add(probationLength, "days");
        const isProbation =
          probationLength > 0 && moment().isBefore(probationEndDate) ? 1 : 0;

        // Apply the same logic as frontend
        let status: string;

        if (daysDifference <= 0) {
          status = "Expired";
        } else if (daysDifference > 0 && daysDifference <= 15) {
          status = "Expiry Soon";
        } else if (
          user.user_contract.is_confirm_sign === true ||
          user.user_contract.is_confirm_sign === 1
        ) {
          status = isProbation === 1 ? "Probation" : "Confirmed";
        } else if (user.user_contract.is_confirm_sign === null) {
          status = "Pending";
        } else {
          status = "Awaiting Signature";
        }

        statusCounts[status as keyof typeof statusCounts]++;
      });

      // Calculate health metrics
      const healthyCount = statusCounts.Confirmed + statusCounts.Probation;
      const problematicCount =
        statusCounts.Expired +
        statusCounts["Expiry Soon"] +
        statusCounts.Pending +
        statusCounts["Awaiting Signature"];
      const totalCount = healthyCount + problematicCount;

      const healthPercentage =
        totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0;

      // Format data for gauge chart
      const gaugeData = [
        {
          name: "Contract Health",
          value: healthPercentage,
          healthy: healthyCount,
          problematic: problematicCount,
          total: totalCount,
          color:
            healthPercentage >= 80
              ? "#10B981"
              : healthPercentage >= 60
                ? "#F59E0B"
                : "#EF4444",
        },
      ];

      return {
        success: true,
        chartType: "gauge",
        title: "Contract Health Metrics",
        data: {
          data: gaugeData,
          nameKey: "name",
          valueKey: "value",
          maxValue: 100,
          unit: "%",
          colors: ["#EF4444", "#F59E0B", "#10B981"], // Red, Orange, Green
          thresholds: [
            { value: 60, color: "#EF4444", label: "Poor" },
            { value: 80, color: "#F59E0B", label: "Fair" },
            { value: 100, color: "#10B981", label: "Good" },
          ],
        },
        metadata: {
          totalRecords: totalCount,
          healthyContracts: healthyCount,
          problematicContracts: problematicCount,
          healthPercentage: healthPercentage,
          breakdown: statusCounts,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getUserContractGaugeData:", error);
      return {
        success: false,
        chartType: "gauge",
        title: "Contract Health Metrics",
        data: {
          data: [],
          nameKey: "name",
          valueKey: "value",
          maxValue: 100,
          unit: "%",
          colors: ["#EF4444", "#F59E0B", "#10B981"],
          thresholds: [],
        },
        metadata: {
          totalRecords: 0,
          healthyContracts: 0,
          problematicContracts: 0,
          healthPercentage: 0,
          breakdown: {
            Expired: 0,
            "Expiry Soon": 0,
            Probation: 0,
            Confirmed: 0,
            Pending: 0,
            "Awaiting Signature": 0,
          },
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get leave comparison data by branch for line chart (Recharts format)
 * Shows 12-month timeline with each branch as a separate line
 * X-axis: 12-month timeline from current month back one year
 * Y-axis: Leave count values
 * Data series: One line per branch with branch colors from database
 */
const getLeaveComparisonByBranchData = async (
  organizationId: string,
  filters?: FilterOptions
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `leave_comparison_${organizationId}_${filters?.branch_ids || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Generate 12-month timeline starting from current month going back one year
      const { monthKeys, monthLabels } = generateMonthRange(12);

      // Build where clauses for branch filtering
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      // Build where clauses for user filtering
      const userWhere: any = {
        organization_id: organizationId,
      };

      // Apply branch filtering if specified
      if (filters?.branch_ids) {
        // Handle both single and multiple branch IDs
        const branchArray = Array.isArray(filters.branch_ids)
          ? filters.branch_ids
          : [filters.branch_ids];

        if (branchArray.length === 1) {
          branchWhere.id = branchArray[0];
          userWhere.branch_id = branchArray[0];
        } else {
          branchWhere.id = { [Op.in]: branchArray };
          userWhere.branch_id = { [Op.in]: branchArray };
        }
      }

      // Execute queries in parallel for better performance
      // Query ALL branches first (regardless of leave data), then query leave data separately
      const [branches, leaveData] = await Promise.all([
        // Query ALL active branches in organization (regardless of leave data)
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name", "branch_color"],
          raw: true,
        }),

        // Query leave requests with user branch information
        UserRequest.findAll({
          where: {
            request_type: "casual",
            request_status: request_status.APPROVED,
          },
          include: [
            {
              model: User,
              as: "request_from_users",
              where: userWhere,
              attributes: ["branch_id"],
              required: true,
            },
          ],
          attributes: ["leave_days", "createdAt"],
          raw: true,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "line",
          title: "Leave Comparison by Branch (12-Month Timeline)",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            timelinePeriod: "12 months",
            branchCount: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Create efficient lookup structures for 12-month data
      const leaveByBranchAndMonth = new Map<string, Map<string, number>>();

      // Initialize data structure - ensure all branches have data for all 12 months
      branches.forEach((branch: any) => {
        const monthMap = new Map<string, number>();
        monthKeys.forEach((key) => monthMap.set(key, 0));
        leaveByBranchAndMonth.set(branch.id.toString(), monthMap);
      });

      // Single pass through leave data to aggregate using Moment.js for 12-month period
      leaveData.forEach((leave: any) => {
        if (leave.createdAt && leave["request_from_users.branch_id"]) {
          // Filter by 12-month date range using DateUtils
          if (DateUtils.isWithinLastMonths(leave.createdAt, 12)) {
            const monthKey = DateUtils.toMonthKey(leave.createdAt);
            const branchId = leave["request_from_users.branch_id"].toString();
            const branchData = leaveByBranchAndMonth.get(branchId);

            if (branchData && branchData.has(monthKey)) {
              const currentTotal = branchData.get(monthKey) || 0;
              branchData.set(monthKey, currentTotal + (leave.leave_days || 0));
            }
          }
        }
      });

      // Format data for Recharts line chart - 12-month timeline with branch lines
      const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
        const dataPoint: RechartsDataPoint = {
          name: label,
          value: 0, // Will be calculated as sum of all branches for total reference
          month: label,
        };

        // Add each branch's data as a separate property for line chart
        branches.forEach((branch: any) => {
          const branchData = leaveByBranchAndMonth.get(branch.id.toString());
          const branchValue = branchData?.get(monthKeys[index]) || 0;
          // Only use branch name as dataKey (remove redundant branch_id property)
          dataPoint[branch.branch_name] = branchValue;
          dataPoint.value += branchValue;
        });

        return dataPoint;
      });

      // Generate series for each branch using database colors
      const defaultColors = [
        "#4F46E5",
        "#10B981",
        "#F59E0B",
        "#EF4444",
        "#8B5CF6",
        "#06B6D4",
      ];
      const series = branches.map((branch: any, index: number) => ({
        dataKey: branch.branch_name,
        name: branch.branch_name,
        color:
          branch.branch_color || defaultColors[index % defaultColors.length],
        type: "line" as const,
      }));

      const totalRecords = leaveData.length;

      return {
        success: true,
        chartType: "line",
        title: "Leave Comparison by Branch (12-Month Timeline)",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series,
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
          timelinePeriod: "12 months",
          branchCount: branches.length,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getLeaveComparisonByBranchData:", error);
      return {
        success: false,
        chartType: "line",
        title: "Leave Comparison by Branch (12-Month Timeline)",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          timelinePeriod: "12 months",
          branchCount: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

/**
 * Get sales comparison data by branch for line chart (Recharts format)
 * Shows 12-month timeline with each branch as a separate line
 * X-axis: 12-month timeline from current month back one year
 * Y-axis: Sales amount values (from DSR data)
 * Data series: One line per branch with branch colors from database
 */
const getSalesComparisonByBranchData = async (
  organizationId: string,
  filters?: any
): Promise<CommonChartResponse> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `sales_comparison_${organizationId}_${filters?.branchId || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Generate 12-month timeline (current month back to 12 months ago)
      const monthKeys: string[] = [];
      const monthLabels: string[] = [];

      for (let i = 11; i >= 0; i--) {
        const monthMoment = moment().subtract(i, "months");
        monthKeys.push(monthMoment.format("YYYY-MM"));
        monthLabels.push(monthMoment.format("MMM YYYY"));
      }

      // Build where clauses for branches
      const branchWhere: any = {
        organization_id: organizationId,
        branch_status: branch_status.ACTIVE,
      };

      // Apply branch filtering if specified
      if (filters?.branchId) {
        branchWhere.id = filters.branchId;
      }

      // Get branches and DSR data in parallel
      const [branches, dsrData] = await Promise.all([
        Branch.findAll({
          where: branchWhere,
          attributes: ["id", "branch_name", "branch_color"],
          raw: true,
        }),
        DsrDetail.findAll({
          where: {
            dsr_detail_status: dsr_detail_status.ACTIVE,
            dsr_date: {
              [Op.gte]: moment()
                .subtract(11, "months")
                .startOf("month")
                .format("YYYY-MM-DD"),
              [Op.lte]: moment().endOf("month").format("YYYY-MM-DD"),
            },
            ...(filters?.branchId && { branch_id: filters.branchId }),
          },
          include: [
            {
              model: DsrItem,
              as: "dsr_detail",
              where: {
                dsr_item_status: dsr_item_status.ACTIVE,
              },
              include: [
                {
                  model: PaymentTypeCategory,
                  as: "dsr_item_type",
                  include: [
                    {
                      model: PaymentType,
                      as: "payment_type_list",
                      where: {
                        payment_type_usage: payment_type_usage.COLLECTION, // Only income/sales
                      },
                    },
                  ],
                },
              ],
              required: false,
            },
          ],
          raw: false,
        }),
      ]);

      if (branches.length === 0) {
        return {
          success: true,
          chartType: "line",
          title: "Sales Comparison by Branch (12-Month Timeline)",
          data: {
            data: [],
            xAxisKey: "name",
            yAxisKey: "value",
            series: [],
          } as RechartsLineBarData,
          metadata: {
            totalRecords: 0,
            timelinePeriod: "12 months",
            branchCount: 0,
            lastUpdated: DateUtils.now(),
          },
        };
      }

      // Create efficient lookup structures for 12-month data
      const salesByBranchAndMonth = new Map<string, Map<string, number>>();

      // Initialize data structure - ensure all branches have data for all 12 months
      branches.forEach((branch: any) => {
        const monthMap = new Map<string, number>();
        monthKeys.forEach((key) => monthMap.set(key, 0));
        salesByBranchAndMonth.set(branch.id.toString(), monthMap);
      });

      // Process DSR data and aggregate sales by branch and month
      dsrData.forEach((dsr: any) => {
        const branchId = dsr.branch_id.toString();
        const monthKey = moment(dsr.dsr_date).format("YYYY-MM");

        if (
          salesByBranchAndMonth.has(branchId) &&
          monthKeys.includes(monthKey)
        ) {
          let totalSales = 0;

          // Sum up all sales items for this DSR entry
          if (dsr.dsr_detail && dsr.dsr_detail.length > 0) {
            totalSales = dsr.dsr_detail.reduce((sum: number, item: any) => {
              return sum + (parseFloat(item.dsr_amount) || 0);
            }, 0);
          }

          const currentAmount =
            salesByBranchAndMonth.get(branchId)!.get(monthKey) || 0;
          salesByBranchAndMonth
            .get(branchId)!
            .set(monthKey, currentAmount + totalSales);
        }
      });

      // Transform to Recharts format
      const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
        const monthKey = monthKeys[index];
        const dataPoint: RechartsDataPoint = {
          name: label,
          value: 0, // This will be the sum of all branches for reference
        };

        // Add each branch's data for this month
        branches.forEach((branch: any) => {
          const branchSales =
            salesByBranchAndMonth.get(branch.id.toString())?.get(monthKey) || 0;
          dataPoint[`branch_${branch.id}`] = branchSales;
          dataPoint.value += branchSales;
        });

        return dataPoint;
      });

      // Create series configuration for each branch
      const series = branches.map((branch: any, index: number) => ({
        dataKey: `branch_${branch.id}`,
        name: branch.branch_name,
        color: branch.branch_color || `hsl(${(index * 137.5) % 360}, 70%, 50%)`, // Fallback colors
        type: "line" as const,
      }));

      const totalRecords = dsrData.length;

      return {
        success: true,
        chartType: "line",
        title: "Sales Comparison by Branch (12-Month Timeline)",
        data: {
          data: chartData,
          xAxisKey: "name",
          yAxisKey: "value",
          series,
        } as RechartsLineBarData,
        metadata: {
          totalRecords,
          dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
          timelinePeriod: "12 months",
          branchCount: branches.length,
          filters: filters,
          lastUpdated: DateUtils.now(),
        },
      };
    } catch (error) {
      console.error("Error in getSalesComparisonByBranchData:", error);
      return {
        success: false,
        chartType: "line",
        title: "Sales Comparison by Branch (12-Month Timeline)",
        data: {
          data: [],
          xAxisKey: "name",
          yAxisKey: "value",
          series: [],
        } as RechartsLineBarData,
        metadata: {
          totalRecords: 0,
          timelinePeriod: "12 months",
          branchCount: 0,
          lastUpdated: DateUtils.now(),
        },
      };
    }
  });
};

export interface DashboardWidget {
  id?: number;
  title: string;
  type: string;
  data: any;
  category: "count" | "chart";
  chartType?:
    | "line"
    | "bar"
    | "pie"
    | "gauge"
    | "multi_line"
    | "meter"
    | "area";
  order?: number;
}

/**
 * Chart configuration mapping system
 * This eliminates hardcoded array indices and makes the system more maintainable
 */
const CHART_CONFIGURATIONS: Record<ChartType, ChartConfig> = {
  [ChartType.ONBOARDING_PIE]: {
    type: ChartType.ONBOARDING_PIE,
    widgetType: "pie_chart",
    title: "Onboarding Pipeline Distribution",
    chartType: "pie",
    category: "chart",
    description:
      "Distribution of onboarding status: Completed and Verified users across branches",
    hasFilters: true,
    dataFetcher: getOnboardingPipelinePieData,
  },
  [ChartType.ONBOARDING_BAR]: {
    type: ChartType.ONBOARDING_BAR,
    widgetType: "bar_chart",
    title: "Onboarding Pipeline Status by Branch",
    chartType: "bar",
    category: "chart",
    description:
      "Compare onboarding status across branches with 2 series: Completed and Verified users",
    hasFilters: true,
    dataFetcher: getOnboardingPipelineBarData,
  },

  [ChartType.CONTRACT_GAUGE]: {
    type: ChartType.CONTRACT_GAUGE,
    widgetType: "gauge_chart",
    title: "Contract Health Metrics",
    chartType: "gauge",
    category: "chart",
    description:
      "Contract health gauge showing percentage of healthy vs problematic contracts",
    hasFilters: true,
    dataFetcher: getUserContractGaugeData,
  },
  [ChartType.LEAVE_COMPARISON]: {
    type: ChartType.LEAVE_COMPARISON,
    widgetType: "line_chart",
    title: "Leave Comparison by Branch (12-Month Timeline)",
    chartType: "line",
    category: "chart",
    description: "Monthly leave data comparison across branches",
    hasFilters: true,
    dataFetcher: getLeaveComparisonByBranchData,
  },
  [ChartType.SALES_COMPARISON]: {
    type: ChartType.SALES_COMPARISON,
    widgetType: "multi_line_chart",
    title: "Income Category Comparison",
    chartType: "multi_line",
    category: "chart",
    description: "Yearly income category comparison",
    hasFilters: true,
    dataFetcher: getSalesComparisonByBranchData,
  },
};

/**
 * Get chart configurations based on filters
 */
const getFilteredChartConfigs = (filters?: FilterOptions): ChartConfig[] => {
  let configs = Object.values(CHART_CONFIGURATIONS); // Filter by chart type if specified
  if (filters?.chartType) {
    configs = configs.filter(
      (config) => config.chartType === filters.chartType
    );
  }

  return configs;
};

/**
 * Create chart widget from configuration and data
 */
const createChartWidget = (
  config: ChartConfig,
  chartData: CommonChartResponse,
  index: number
): DashboardWidget => {
  // Generate dynamic ID based on chart type and index
  const baseId = 5; // Start chart IDs after the 4 count widgets
  const widgetId = baseId + index;

  return {
    id: widgetId,
    title: chartData.title || config.title,
    type: config.widgetType,
    data: {
      // Use the data from chartData.data directly to avoid duplication
      ...chartData.data,
      ...(config.hasFilters && { filters: { branchFilter: true } }),
      description: config.description,
    },
    category: config.category,
    chartType: config.chartType,
    order: widgetId, // Add the missing order property
  };
};

/**
 * Interface for User Statistics
 */
export interface UserStatistics {
  totalUsers: number;
  pendingUsers: number;
  activeUsers: number;
  ongoingUsers: number;
  completedUsers: number;
  verifiedUsers: number;
  deletedUsers: number;
}

/**
 * Interface for Employee Leave Statistics
 */
export interface EmployeeLeaveStatistics {
  employeesOnLeaveToday: number;
  employeesWithRequestedLeave: number;
}

/**
 * Interface for Change Request Statistics
 */
export interface ChangeRequestStatistics {
  totalChangeRequests: number;
  pendingChangeRequests: number;
  approvedChangeRequests: number;
  rejectedChangeRequests: number;
}

/**
 * Interface for Resignation Statistics
 */
export interface ResignationStatistics {
  totalResignations: number;
  pendingResignations: number;
  approvedResignations: number;
  processedResignations: number;
}

//  Interface for active branch & department statistics
export interface BranchDepartmentStatistics {
  activeBranches: number;
  activeDepartments: number;
}

/**
 * Interface for Rota Statistics
 */
export interface RotaStatistics {
  shiftSwapCount: number;
  dropShiftCount: number;
  totalShiftCount: number;
}

/**
 * Interface for Role Statistics (Simple number cards)
 */
export interface RoleStatistics {
  totalRoleCount: number;
  activeRoleCount: number;
  inactiveRoleCount: number;
}

/**
 * Interface for Leave Type & Policy Statistics (Simple number cards)
 * Note: Only showing active counts as totals per user request
 */
export interface LeaveTypePolicyStatistics {
  totalLeaveTypes: number; // Active leave types count
  totalLeavePolicies: number; // Active leave policies count
}

/**
 * Interface for Document Statistics (Organized structure)
 * Enhanced to show training vs document breakdown with files/folders
 */
export interface DocumentStatistics {
  // Overview
  totalDocuments: number; // Total document items (files)
  totalCategories: number; // Total active categories (training + document)

  // Training Section
  training: {
    totalCategories: number; // Training categories count
    files: number; // Files in training categories
    folders: number; // Folders in training categories
  };

  // Document Section
  documents: {
    totalCategories: number; // Document categories count
    files: number; // Files in document categories
    folders: number; // Folders in document categories
  };
}

/**
 * Interface for Recipe Statistics (Simple 3 counts)
 * Clean and focused recipe breakdown
 */
export interface RecipeStatistics {
  totalRecipes: number; // Total active recipes (not deleted)
  draftRecipes: number; // Draft recipes
  publishedRecipes: number; // Published recipes
}

/**
 * Interface for Holiday Statistics (Simple number cards)
 * Note: Only showing active counts as totals per user request
 */
export interface HolidayStatistics {
  totalHolidays: number; // Active holidays count
  totalHolidayTypes: number; // Active holiday types count
}

/**
 * Interface for Complete Dashboard Statistics
 */
export interface DashboardStatistics {
  userStats: UserStatistics;
  leaveStats: EmployeeLeaveStatistics;
  changeRequestStats: ChangeRequestStatistics;
  resignationStats: ResignationStatistics;
  rotaStats: RotaStatistics;
  branchDeptStats: BranchDepartmentStatistics;
  contractStats: any; // Contract statistics with general and department-wise breakdown
  roleStats: RoleStatistics;
  leaveTypePolicyStats: LeaveTypePolicyStatistics;
  documentStats: DocumentStatistics;
  recipeStats: RecipeStatistics;
  holidayStats: HolidayStatistics;
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users with user-specific statistics
 */
export const getUserDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: FilterOptions
): Promise<DashboardWidget[]> => {
  // Validate input parameters
  validateParameters(organizationId, userId);

  try {
    // Get user details to filter statistics by user's branch/department
    const userDetails = await User.findOne({
      where: { id: userId, organization_id: organizationId },
      attributes: ["id", "branch_id", "department_id", "organization_id"],
      raw: true,
    });

    if (!userDetails) {
      throw new Error(
        `User ${userId} not found in organization ${organizationId}`
      );
    }
    // Dynamic count widgets with real database queries
    const countWidgets: DashboardWidget[] = [
      {
        id: 1,
        title: "My Tasks",
        type: "task_summary",
        data: await getMyTaskStatistics(
          organizationId,
          userId,
          filters?.branch_ids
        ),
        order: 1,
        category: "count",
      },
      {
        id: 2,
        title: "My Leave Balance",
        type: "leave_balance",
        data: await getMyLeaveBalanceStatistics(organizationId, userId),
        order: 2,
        category: "count",
      },
      {
        id: 3,
        title: "User Activity",
        type: "user_activity",
        data: await getUserActivityStatistics(
          organizationId,
          filters?.activity_action
            ? { activity_action: filters.activity_action, userId: userId }
            : { userId: userId }
        ),
        order: 3,
        category: "count",
      },
      {
        id: 4,
        title: "Notification Center",
        type: "notification_center",
        data: await getNotificationCenterStatistics(
          organizationId,
          filters?.notification_status
            ? {
                notification_status: filters.notification_status,
                userId: userId,
              }
            : { userId: userId }
        ),
        order: 4,
        category: "count",
      },
      {
        id: 5,
        title: "My Performance",
        type: "performance_chart",
        data: await getMyPerformanceStatistics(organizationId, userId),
        order: 5,
        category: "count",
      },
    ];

    // Get filtered chart configurations based on user filters
    const chartConfigs = getFilteredChartConfigs(filters);

    // Create data fetching promises dynamically based on configurations
    const chartDataPromises = chartConfigs.map((config) => ({
      type: config.type,
      promise: config.dataFetcher(organizationId, filters),
    }));

    // Use Promise.allSettled to handle partial failures gracefully
    const chartResults = await Promise.allSettled(
      chartDataPromises.map((item) => item.promise)
    );

    // Create chart widgets dynamically using the mapping system
    const chartWidgets: DashboardWidget[] = [];

    chartResults.forEach((result, index) => {
      const config = chartConfigs[index];
      const chartType = chartDataPromises[index].type;

      if (result.status === "fulfilled" && result.value.success) {
        // Create widget using the configuration and data
        const widget = createChartWidget(config, result.value, index);
        chartWidgets.push(widget);
      } else {
        // Log failed chart data fetches for monitoring
        const errorMessage =
          result.status === "rejected"
            ? result.reason
            : "Chart data fetch returned unsuccessful result";
        console.error(
          `Chart data fetch failed for ${chartType}:`,
          errorMessage
        );
      }
    });

    // Get user-specific statistics (filtered by user's branch)
    let statisticsData = null;
    try {
      // Use user's branch for filtering statistics instead of organization-wide
      const userBranchFilter = userDetails.branch_id
        ? { branch_ids: userDetails.branch_id }
        : undefined;

      statisticsData = await getComprehensiveStatistics(
        organizationId,
        // Combine user's branch filter with any additional filters
        filters?.branch_ids
          ? { branch_ids: filters.branch_ids }
          : userBranchFilter
      );
    } catch (error) {
      console.error(
        "Failed to fetch user-specific statistics data, continuing with basic widgets:",
        error
      );
    }

    // Add user statistics widget if statistics data is available
    if (statisticsData) {
      countWidgets.push({
        id: 5,
        title: "User Statistics",
        type: "user_statistics",
        data: statisticsData.userStats,
        order: 5,
        category: "count",
      });

      // Add rota statistics widget
      countWidgets.push({
        id: 6,
        title: "Rota Statistics",
        type: "rota_statistics",
        data: statisticsData.rotaStats,
        order: 6,
        category: "count",
      });
      // Add branch & department statistics widget
      countWidgets.push({
        id: 7,
        title: "Branch & Department Statistics",
        type: "branch_department_statistics",
        data: statisticsData.branchDeptStats,
        order: 7,
        category: "count",
      });

      // Add contract statistics widget (Simple number cards with department breakdown)
      countWidgets.push({
        id: 8,
        title: "Number of contract",
        type: "contract_statistics",
        data: statisticsData.contractStats,
        order: 8,
        category: "count",
      });

      // Add role statistics widget (Simple number cards)
      countWidgets.push({
        id: 9,
        title: "Roles",
        type: "role_statistics",
        data: statisticsData.roleStats,
        order: 9,
        category: "count",
      });

      // Add leave type & policy statistics widget (Simple number cards)
      countWidgets.push({
        id: 10,
        title: "Leave Types & Policies",
        type: "leave_type_policy_statistics",
        data: statisticsData.leaveTypePolicyStats,
        order: 10,
        category: "count",
      });

      // Add document statistics widget (Simple number cards)
      countWidgets.push({
        id: 11,
        title: "Documents",
        type: "document_statistics",
        data: statisticsData.documentStats,
        order: 11,
        category: "count",
      });

      // Add recipe statistics widget (Simple number cards)
      countWidgets.push({
        id: 12,
        title: "Recipes",
        type: "recipe_statistics",
        data: statisticsData.recipeStats,
        order: 12,
        category: "count",
      });

      // Add holiday statistics widget (Simple number cards)
      countWidgets.push({
        id: 13,
        title: "Holidays",
        type: "holiday_statistics",
        data: statisticsData.holidayStats,
        order: 13,
        category: "count",
      });
    }

    // Combine widgets based on filter requirements
    let allWidgets: DashboardWidget[] = [];

    if (filters?.widgetType === "charts") {
      allWidgets = chartWidgets;
    } else if (filters?.widgetType === "counts") {
      allWidgets = countWidgets;
    } else {
      // Default: return both count and chart widgets
      allWidgets = [...countWidgets, ...chartWidgets];
    }

    // Apply chart type filtering if specified
    if (filters?.widgetType === "charts" && filters?.chartType) {
      allWidgets = allWidgets.filter(
        (widget) => widget.chartType === filters.chartType
      );
    }

    return allWidgets;
  } catch (error) {
    console.error("Error in getUserDashboardWidgets:", error);
    // Return at least the count widgets on error to provide partial functionality
    if (filters?.widgetType !== "charts") {
      return [
        {
          id: 1,
          title: "My Tasks",
          type: "task_summary",
          data: { pending: 0, completed: 0, overdue: 0 },
          category: "count",
          order: 1,
        },
      ];
    }
    throw error;
  }
};
const getDsrDataByTimePeriod = async (
  organizationId: string,
  timePeriod: string,
  branchId?: string,
  chartType?: string,
  payment_type_category_id?: string,
  year?: string
): Promise<any> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const settings: any = await getGeneralSettingObj(organizationId);
  try {
    let whereClause = `1=1`; // Start with a clause that is always true

    if (chartType == "meter" || chartType == "area") {
      timePeriod = "this_year";
    }
    const timePeriodConfig = getTimePeriodConfig(
      timePeriod,
      year,
      settings.financial_month
    );
    const { startDate, endDate, labels, labelKeyMap } = timePeriodConfig;

    if (branchId) {
      whereClause += ` AND b.id IN(${branchId.split(",")})`;
    }
    if (payment_type_category_id) {
      whereClause += ` AND ptc.id IN(${payment_type_category_id})`;
    }
    if (chartType == "meter" || chartType == "area") {
      whereClause += ` AND data.payment_type_usage = '${payment_type_usage.COLLECTION}'`;
    }

    const getReportQuery = `SELECT 
        ptc.id AS payment_type_category_id,
        ptc.payment_type_category_title AS category_name,
        data.branch_id,
        b.branch_name,
        data.start_date,
        data.end_date,
        data.amount,
        data.type,
        data.details_id,
        data.payment_type_usage,
        DAYNAME(data.start_date) AS day_name,
        MONTHNAME(data.start_date) AS month_name,
        YEAR(data.start_date) AS year,
        WEEK(data.start_date) AS week_of_year,
        QUARTER(data.start_date) AS quarter

        FROM nv_payment_type_category ptc

        LEFT JOIN (
        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id, di.dsr_amount AS amount,
                di.payment_type_category_id, 'dsr' AS type, dd.id AS details_id, pt.payment_type_usage
            FROM nv_dsr_items di
            JOIN nv_dsr_details dd ON dd.id = di.dsr_detail_id
            JOIN nv_payment_type_category ptc ON ptc.id = di.payment_type_category_id
            JOIN nv_payment_type pt ON pt.id = ptc.payment_type_id
            WHERE di.dsr_item_status = 'active' AND dd.dsr_detail_status = 'active'
            AND dd.dsr_date BETWEEN '${startDate}' AND '${endDate}'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id, wi.wsr_amount AS amount,
                wi.payment_type_category_id, 'wsr' AS type, wd.id AS details_id, pt.payment_type_usage
            FROM nv_wsr_items wi
            JOIN nv_wsr_details wd ON wd.id = wi.wsr_detail_id
            JOIN nv_payment_type_category ptc ON ptc.id = wi.payment_type_category_id
            JOIN nv_payment_type pt ON pt.id = ptc.payment_type_id
            WHERE wi.wsr_item_status = 'active' AND wd.wsr_detail_status = 'active'
            AND wd.wsr_start_date BETWEEN '${startDate}' AND '${endDate}'

        UNION ALL

        SELECT 
            DATE_FORMAT(CONCAT(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-01') AS start_date,
            LAST_DAY(CONCAT(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date,
            ed.branch_id,
            ei.expense_amount AS amount,
            ei.payment_type_category_id,
            'expense' AS type,
            ed.id AS details_id,
            pt.payment_type_usage
            FROM nv_expense_items ei
            JOIN nv_expense_details ed ON ed.id = ei.expense_detail_id
            JOIN nv_payment_type_category ptc ON ptc.id = ei.payment_type_category_id
            JOIN nv_payment_type pt ON pt.id = ptc.payment_type_id
            WHERE ei.expense_item_status = 'active' AND ed.expense_detail_status = 'active'
            AND CONCAT(ed.expense_year, '-', LPAD(ed.expense_month, 2, '0'), '-01') BETWEEN '${startDate}' AND '${endDate}'
        ) data ON data.payment_type_category_id = ptc.id

        LEFT JOIN nv_branches b ON b.id = data.branch_id AND b.organization_id = '${organizationId}'

        WHERE ${whereClause}  
        ORDER BY ptc.id;`;
    console.log("getReportQuery", getReportQuery);
    const reportData: any = await sequelize.query(getReportQuery, {
      type: QueryTypes.SELECT,
    });
    if (chartType === "meter") {
      let getBudgetQuery = `SELECT SUM(fbd.bugdet_target_amount) AS total_budget_amount
            FROM nv_forecast_bugdet_data AS fbd
            JOIN nv_forecast AS f ON f.id = fbd.forecast_id
            WHERE f.forecast_year = ${year ? year : new Date().getFullYear()} AND fbd.forecast_category_type = '${forecast_category_type.INCOME}'`;

      if (branchId) {
        getBudgetQuery += ` AND f.branch_id IN(${branchId.split(",")})`;
      }

      const budgetData: any = await sequelize.query(getBudgetQuery, {
        type: QueryTypes.SELECT,
      });

      const totalBudgetAmount =
        budgetData.length > 0
          ? parseFloat(budgetData[0].total_budget_amount) || 0
          : 0;
      const totalActualAmount = reportData.reduce(
        (sum: any, item: any) => sum + (parseFloat(item.amount) || 0),
        0
      );

      return {
        success: true,
        dashboard_data: {
          min: 0,
          value: totalActualAmount,
          max: totalBudgetAmount == 0 ? totalActualAmount : totalBudgetAmount,
          remaining:
            totalBudgetAmount == 0 ? 0 : totalBudgetAmount - totalActualAmount,
          thresholds: ["#E74C3C", "#F39C3C", "#F7DC6F", "#82E0AA", "#229954"],
          unit: "",
          meterType: "absolute",
        },
      };
    }

    if (chartType === "area") {
      let whereClause = ``;
      if (branchId) {
        whereClause += ` AND f.branch_id IN(${branchId.split(",")})`;
      }
      const getBudgetQuery: any = `SELECT
                fbd.forecast_category_type,
                SUM(fbd.january_amount) AS January,
                SUM(fbd.february_amount) AS February,
                SUM(fbd.march_amount) AS March,
                SUM(fbd.april_amount) AS April,
                SUM(fbd.may_amount) AS May,
                SUM(fbd.june_amount) AS June,
                SUM(fbd.july_amount) AS July,
                SUM(fbd.august_amount) AS August,
                SUM(fbd.september_amount) AS September,
                SUM(fbd.october_amount) AS October,
                SUM(fbd.november_amount) AS November,
                SUM(fbd.december_amount) AS December,
                GROUP_CONCAT(fbd.forecast_category_status) AS forecast_category_status,
                pt.payment_type_title,	
                fbd.payment_type_id,
                fbd.payment_type_category_id,
                pm.payment_type_category_title AS payment_type_category_title,
                pt.has_field_currency,
                MAX(f.forecast_year) AS forecast_year
            FROM
                nv_forecast_bugdet_data fbd
            LEFT JOIN
                nv_payment_type pt
                ON pt.id = fbd.payment_type_id
            LEFT JOIN
                nv_payment_type_category pm
                ON pm.id = fbd.payment_type_category_id
            LEFT JOIN
                nv_forecast f
                ON f.id = fbd.forecast_id
            WHERE
                fbd.forecast_bugdet_data_status != 'inactive'
                AND f.forecast_status != 'deleted' AND pt.organization_id='${organizationId}' AND f.forecast_year = ${year ? year : new Date().getFullYear()} AND fbd.forecast_category_type = '${forecast_category_type.INCOME}'
                ${whereClause}
                GROUP BY fbd.forecast_category_type, fbd.payment_type_id, fbd.payment_type_category_id
                ORDER BY fbd.payment_type_id ASC, fbd.payment_type_category_id ASC;`;
      const budgetData: any = await sequelize.query(getBudgetQuery, {
        type: QueryTypes.SELECT,
      });

      const MONTHS = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ];

      const financialMonthRange = settings?.financial_month?.toLowerCase(); // e.g. "april - march"

      // Extract start and end month
      const [startMonthStr] = financialMonthRange?.split(" - ") || ["january"];
      const startMonthIndex = MONTHS.findIndex(
        (m) => m.toLowerCase() === startMonthStr.trim()
      );

      // Reorder months based on financial year start
      const orderedMonths = [
        ...MONTHS.slice(startMonthIndex),
        ...MONTHS.slice(0, startMonthIndex),
      ];

      // Initialize response object
      const result = {
        series: [
          {
            type: "line",
            xKey: "col1",
            yKey: "col2",
            yName: "income",
            fills: [],
          },
          {
            type: "area",
            xKey: "col1",
            yKey: "col3",
            yName: "target",
            fills: [],
          },
        ],
        data: orderedMonths.map((month) => ({
          col1: month,
          col2: 0, // actual
          col3: 0, // target (budget)
        })),
      };

      // --- 1. Populate budget (col3) ---
      budgetData
        .filter((item: any) => item.forecast_category_type === "income")
        .forEach((item: any) => {
          MONTHS.forEach((month) => {
            const value = parseFloat(item[month]) || 0;
            const targetMonth = result.data.find((d) => d.col1 === month);
            if (targetMonth) targetMonth.col3 += value;
          });
        });

      // --- 2. Populate actual income (col2) ---
      reportData
        .filter((item: any) => item.payment_type_usage === "income")
        .forEach((entry: any) => {
          const month = entry.month_name;
          const target = result.data.find((d) => d.col1 === month);
          if (target) target.col2 += entry.amount;
        });

      return {
        success: true,
        dashboard_data: result,
      };
    }

    if (chartType === "line" && payment_type_category_id) {
      const categoryIds = payment_type_category_id
        .split(",")
        .map((id) => parseInt(id.trim(), 10));

      const categories = await PaymentTypeCategory.findAll({
        where: {
          id: {
            [Op.in]: categoryIds,
          },
        },
        attributes: ["id", "payment_type_category_title"],
        raw: true,
      });

      const categoryMap = new Map<number, string>();
      categories.forEach((cat: any) =>
        categoryMap.set(cat.id, cat.payment_type_category_title)
      );

      const dataMap = new Map<string, any>();
      labels.forEach((label) => {
        const initialData: any = { col1: label };
        categories.forEach((_, i) => {
          initialData[`col${i + 2}`] = 0; // col2, col3, col4, etc.
        });
        dataMap.set(label, initialData);
      });

      reportData.forEach((record: any) => {
        let key: string;
        if (timePeriodConfig.groupBy === "day") {
          key = record.day_name;
        } else if (timePeriodConfig.groupBy === "week") {
          key = `Week ${record.week_of_year}`;
        } else if (timePeriodConfig.groupBy === "month") {
          key = record.month_name;
        } else {
          key = record.start_date;
        }
        key = labelKeyMap[key] || key;

        const amount = parseFloat(record.amount) || 0;
        const categoryId = record.payment_type_category_id;

        if (labels.includes(key) && categoryIds.includes(categoryId)) {
          const categoryIndex = categories.findIndex(
            (cat: any) => cat.id === categoryId
          );
          if (categoryIndex !== -1) {
            const dataEntry = dataMap.get(key);
            if (dataEntry) {
              dataEntry[`col${categoryIndex + 2}`] += amount;
            }
          }
        }
      });

      const chartData = Array.from(dataMap.values());
      const series = categories.map((category, i) => ({
        type: "line",
        xKey: "col1",
        yKey: `col${i + 2}`,
        yName: category.payment_type_category_title,
        fills: [],
      }));

      return {
        success: true,
        dashboard_data: {
          series: series,
          data: chartData,
        },
      };
    } else {
      // Fallback for existing multi_line logic if needed, or other chart types

      const dsrMap = new Map<string, number>();
      const wsrMap = new Map<string, number>();
      const expenseMap = new Map<string, number>();

      labels.forEach((label: any) => {
        dsrMap.set(label, 0);
        wsrMap.set(label, 0);
        expenseMap.set(label, 0);
      });
      reportData.forEach((record: any) => {
        let key: string;

        // Generate key based on groupBy type using existing query fields
        if (timePeriodConfig.groupBy === "day") {
          key = record.day_name; // Sunday, Monday, etc.
        } else if (timePeriodConfig.groupBy === "week") {
          // Use actual week_of_year from query (Week 27, Week 28, etc.)
          key = `Week ${record.week_of_year}`;
        } else if (timePeriodConfig.groupBy === "month") {
          key = record.month_name; // January, February, etc.
        } else {
          key = record.start_date; // fallback
        }
        // Normalize key using labelKeyMap
        key = labelKeyMap[key] || key;
        const amount = parseFloat(record.amount) || 0;

        if (labels.includes(key)) {
          switch (record.type) {
            case "dsr":
              dsrMap.set(key, (dsrMap.get(key) || 0) + amount);
              break;
            case "wsr":
              wsrMap.set(key, (wsrMap.get(key) || 0) + amount);
              break;
            case "expense":
              expenseMap.set(key, (expenseMap.get(key) || 0) + amount);
              break;
          }
        }
      });
      // Prepare data based on chart type
      let data = [];
      let series = [];

      // For multi_line chart, keep separate series for DSR, WSR, and Expense
      data = labels.map((label) => ({
        col1: label,
        col2: dsrMap.get(label) || 0,
        col3: wsrMap.get(label) || 0,
        col4: expenseMap.get(label) || 0,
      }));

      series = [
        { type: "line", xKey: "col1", yKey: "col2", yName: "DSR", fills: [] },
        { type: "line", xKey: "col1", yKey: "col3", yName: "WSR", fills: [] },
        {
          type: "line",
          xKey: "col1",
          yKey: "col4",
          yName: "Expense",
          fills: [],
        },
      ];

      return {
        success: true,
        dashboard_data: {
          series: series,
          data: data,
        },
      };
    }
  } catch (error) {
    console.error("Error in getDsrDataByTimePeriod:", error);

    // Prepare empty response based on chart type
    let series: any = [];

    if (chartType === "line") {
      series = [];
    } else {
      series = [
        { type: "line", xKey: "col1", yKey: "col2", yName: "DSR", fills: [] },
        { type: "line", xKey: "col1", yKey: "col3", yName: "WSR", fills: [] },
        {
          type: "line",
          xKey: "col1",
          yKey: "col4",
          yName: "Expense",
          fills: [],
        },
      ];
    }

    return {
      success: false,
      dashboard_data: {
        series: series,
        data: [],
      },
    };
  }
};

export const getDsrDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: any
): Promise<DashboardWidget[]> => {
  validateParameters(organizationId, userId);

  try {
    // Extract time period filter (default to 'this_month')
    const timePeriod = filters?.filter_time_period;
    const branchId = filters?.branchId;
    const chartType = filters?.chartType || "multi_line"; // Default to multi_line
    const payment_type_category_id = filters?.payment_type_category_id;
    const year = filters?.year;

    // Get DSR data based on time period
    const dsrResult = await getDsrDataByTimePeriod(
      organizationId,
      timePeriod,
      branchId,
      chartType,
      payment_type_category_id,
      year
    );

    let countWidgets: DashboardWidget[] = [];
    if (chartType == "meter") {
      countWidgets = [
        {
          id: 1,
          title: "Performance Meter",
          type: "meter",
          data: dsrResult.dashboard_data,
          order: 1,
          category: "chart",
          chartType: chartType,
        },
      ];
    } else if (chartType === "line") {
      countWidgets = [
        {
          id: 1,
          title: "Income Summary",
          type: "dsr_summary",
          data: dsrResult.dashboard_data,
          order: 1,
          category: "chart",
          chartType: chartType,
        },
      ];
    } else {
      countWidgets = [
        {
          id: 1,
          title: "DSR/WSR/Expense Summary",
          type: "dsr_summary",
          data: dsrResult.dashboard_data,
          order: 1,
          category: "chart",
          chartType: chartType,
        },
      ];
    }
    return countWidgets;
  } catch (error) {
    console.error("Error in getDsrDashboardWidgets:", error);
    // Return empty DSR widget on error
    return [
      {
        id: 1,
        title: "DSR/WSR/Expense Summary",
        type: "dsr_summary",
        data: {
          series: [
            {
              type: "line",
              xKey: "col1",
              yKey: "col2",
              yName: "DSR",
              fills: [],
            },
            {
              type: "line",
              xKey: "col1",
              yKey: "col3",
              yName: "WSR",
              fills: [],
            },
            {
              type: "line",
              xKey: "col1",
              yKey: "col4",
              yName: "Expense",
              fills: [],
            },
          ],
          data: [],
        },
        order: 1,
        category: "chart",
        chartType: "line",
      },
    ];
  }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
  organizationId: string,
  userId: number,
  filters?: any
): Promise<DashboardWidget[]> => {
  // Suppress unused parameter warnings - these parameters are kept for API consistency
  void userId;
  try {
    const widgets: DashboardWidget[] = [];

    // Get comprehensive statistics for admin users
    let statisticsData = null;
    try {
      statisticsData = await getComprehensiveStatistics(
        organizationId,
        filters?.branch_ids ? { branch_ids: filters.branch_ids } : undefined
      );
    } catch (error) {
      console.warn(
        "Failed to fetch statistics data, continuing with basic widgets:",
        error
      );
    }

    // Setup-specific widgets
    widgets.push({
      id: 1,
      title: "System Configuration",
      type: "system_config",
      data: {
        completedSteps: 8,
        totalSteps: 12,
        progressPercentage: 67,
      },
      order: 1,
      category: "count",
    });

    widgets.push({
      id: 2,
      title: "User Setup",
      type: "user_setup",
      data: {
        totalUsers: 25,
        activeUsers: 20,
        pendingInvitations: 5,
      },
      order: 2,
      category: "count",
    });

    widgets.push({
      id: 3,
      title: "Module Configuration",
      type: "module_config",
      data: {
        enabledModules: 8,
        totalModules: 12,
        pendingConfiguration: 4,
      },
      order: 3,
      category: "count",
    });

    widgets.push({
      id: 4,
      title: "Integration Status",
      type: "integration_status",
      data: {
        connectedServices: 3,
        totalServices: 6,
        failedConnections: 1,
      },
      order: 4,
      category: "count",
    });

    // Add change request and resignation statistics for setup dashboard
    if (statisticsData) {
      widgets.push({
        id: 5,
        title: "Change Requests",
        type: "change_request_statistics",
        data: statisticsData.changeRequestStats,
        order: 5,
        category: "count",
      });

      widgets.push({
        id: 6,
        title: "Resignations",
        type: "resignation_statistics",
        data: statisticsData.resignationStats,
        order: 6,
        category: "count",
      });
    }

    return widgets;
  } catch (error) {
    console.error("Error in getSetupDashboardWidgets:", error);
    throw error;
  }
};

/**
 * Get contract statistics (General + Department-wise breakdown)
 */
const getContractStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<any> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `contract_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build user where clause with organization and branch filtering
      const userWhereClause: any = {
        organization_id: organizationId,
      };

      // Add branch filtering if specified
      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          if (branchArray.length === 1) {
            userWhereClause.branch_id = branchArray[0];
          } else {
            userWhereClause.branch_id = { [Op.in]: branchArray };
          }
        }
      }

      // Count available contract templates by category 
      const templateStats = await sequelize.query(
        `SELECT
          'General' as template_category,
          COUNT(DISTINCT ct.id) as contract_count
         FROM nv_contract_template ct
         INNER JOIN nv_contract_category cc ON ct.category_id = cc.id
         WHERE cc.organization_id = :organizationId
         AND ct.status = 'active'
         AND cc.type = 'general'

         UNION ALL

         SELECT
          COALESCE(d.department_name, CONCAT('Department_', cc.department_id)) as template_category,
          COUNT(DISTINCT ct.id) as contract_count
         FROM nv_contract_template ct
         INNER JOIN nv_contract_category cc ON ct.category_id = cc.id
         LEFT JOIN nv_departments d ON cc.department_id = d.id
         WHERE cc.organization_id = :organizationId
         AND ct.status = 'active'
         AND cc.type = 'department'
         GROUP BY cc.department_id, d.department_name
         HAVING COUNT(DISTINCT ct.id) > 0
         ORDER BY template_category`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );

      // Build the result object with template categories as keys
      const result: any = {};

      // Process template usage counts - both general and department templates
      if (templateStats && Array.isArray(templateStats)) {
        (templateStats as any[]).forEach((template: any) => {
          const contractCount = parseInt(template.contract_count) || 0;
          const categoryName = template.template_category;

          // Only add categories that have contracts and valid names
          if (contractCount > 0 && categoryName && categoryName.trim() !== "") {
            result[categoryName] = contractCount;
          }
        });
      }

      return result;
    } catch (error: any) {
      console.error("Error fetching contract statistics:", error);
      console.error(
        "Contract statistics error details:",
        error?.message || error
      );
      return {
        totalContracts: 0,
        departmentWise: [],
      };
    }
  });
};

/**
 * Get comprehensive statistics data for dashboard widgets
 * This function aggregates all statistics in one place following the existing pattern
 * Supports filtering for specific statistics
 */
const getComprehensiveStatistics = async (
  organizationId: string,
  filters?: { branch_ids?: string | number | number[] }
) => {
  try {
    // Execute all statistics queries in parallel for better performance
    const [
      userStats,
      leaveStats,
      changeRequestStats,
      resignationStats,
      rotaStats,
      branchDeptStats,
      contractStats,
      roleStats,
      leaveTypePolicyStats,
      documentStats,
      recipeStats,
      holidayStats,
    ] = await Promise.all([
      getUserStatistics(organizationId, filters?.branch_ids),
      getEmployeeLeaveStatistics(organizationId, filters?.branch_ids),
      getChangeRequestStatistics(organizationId, filters?.branch_ids),
      getResignationStatistics(organizationId, filters?.branch_ids),
      getRotaStatistics(organizationId, filters?.branch_ids),
      getBranchDepartmentStatistics(organizationId, filters?.branch_ids),
      getContractStatistics(organizationId, filters?.branch_ids),
      getRoleStatistics(organizationId),
      getLeaveTypePolicyStatistics(organizationId),
      getDocumentStatistics(organizationId, filters?.branch_ids),
      getRecipeStatistics(organizationId),
      getHolidayStatistics(organizationId),
    ]);

    return {
      userStats,
      leaveStats,
      changeRequestStats,
      resignationStats,
      rotaStats,
      branchDeptStats,
      contractStats,
      roleStats,
      leaveTypePolicyStats,
      documentStats,
      recipeStats,
      holidayStats,
    };
  } catch (error) {
    console.error("Error in getComprehensiveStatistics:", error);
    throw error;
  }
};

// ============================================================================
// STATISTICS FUNCTIONS
// ============================================================================

/**
 * Get user statistics for the organization with branch filtering support
 */
const getUserStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<UserStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `user_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build where clause with organization filter
      const whereClause: any = {
        organization_id: organizationId,
      };

      // Add branch filtering if specified
      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          if (branchArray.length === 1) {
            whereClause.branch_id = branchArray[0];
          } else {
            whereClause.branch_id = { [Op.in]: branchArray };
          }
        }
      }

      // fetch user statistics with proper filtering
      const userStatsQuery = await User.findOne({
        attributes: [
          [sequelize.fn("COUNT", sequelize.col("*")), "total_users"],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN user_status = '${user_status.PENDING}' THEN 1 ELSE 0 END`
              )
            ),
            "total_pending",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN user_status = '${user_status.ACTIVE}' THEN 1 ELSE 0 END`
              )
            ),
            "total_active",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN user_status = '${user_status.ONGOING}' THEN 1 ELSE 0 END`
              )
            ),
            "total_ongoing",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN user_status = '${user_status.COMPLETED}' THEN 1 ELSE 0 END`
              )
            ),
            "total_completed",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN user_status = '${user_status.VERIFIED}' THEN 1 ELSE 0 END`
              )
            ),
            "total_verified",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN user_status = '${user_status.DELETED}' THEN 1 ELSE 0 END`
              )
            ),
            "total_deleted",
          ],
        ],
        where: whereClause,
        raw: true,
        subQuery: false,
      });

      return {
        totalUsers:
          parseInt((userStatsQuery as any)?.total_users as string) || 0,
        pendingUsers:
          parseInt((userStatsQuery as any)?.total_pending as string) || 0,
        activeUsers:
          parseInt((userStatsQuery as any)?.total_active as string) || 0,
        ongoingUsers:
          parseInt((userStatsQuery as any)?.total_ongoing as string) || 0,
        completedUsers:
          parseInt((userStatsQuery as any)?.total_completed as string) || 0,
        verifiedUsers:
          parseInt((userStatsQuery as any)?.total_verified as string) || 0,
        deletedUsers:
          parseInt((userStatsQuery as any)?.total_deleted as string) || 0,
      };
    } catch (error) {
      console.error("Error fetching user statistics:", error);
      // Return default values on error to prevent dashboard from breaking
      return {
        totalUsers: 0,
        pendingUsers: 0,
        activeUsers: 0,
        ongoingUsers: 0,
        completedUsers: 0,
        verifiedUsers: 0,
        deletedUsers: 0,
      };
    }
  });
};

/**
 * Get employee leave statistics for the organization with branch filtering support
 */
const getEmployeeLeaveStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<EmployeeLeaveStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `leave_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      const today = moment().format("YYYY-MM-DD");

      // Build user where clause with organization and branch filtering
      const userWhereClause: any = {
        organization_id: organizationId,
      };

      // Add branch filtering if specified
      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          if (branchArray.length === 1) {
            userWhereClause.branch_id = branchArray[0];
          } else {
            userWhereClause.branch_id = { [Op.in]: branchArray };
          }
        }
      }

      // Count employees on leave today (approved leave requests that include today's date)
      const employeesOnLeaveToday = await UserRequest.count({
        include: [
          {
            model: User,
            as: "request_from_users", // Use the proper association alias
            where: userWhereClause,
            attributes: [], // Don't select user attributes for better performance
          },
        ],
        where: {
          request_status: "approved",
          start_date: { [Op.lte]: today },
          end_date: { [Op.gte]: today },
        },
      });

      // Count employees with pending leave requests
      const employeesWithRequestedLeave = await UserRequest.count({
        include: [
          {
            model: User,
            as: "request_from_users", // Use the proper association alias
            where: userWhereClause,
            attributes: [], // Don't select user attributes for better performance
          },
        ],
        where: {
          request_status: "pending",
        },
      });

      return {
        employeesOnLeaveToday,
        employeesWithRequestedLeave,
      };
    } catch (error) {
      console.error("Error fetching employee leave statistics:", error);
      // Return default values on error
      return {
        employeesOnLeaveToday: 0,
        employeesWithRequestedLeave: 0,
      };
    }
  });
};

/**
 * Get change request statistics for the organization with branch filtering support
 */
const getChangeRequestStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<ChangeRequestStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `change_request_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build user where clause with organization and branch filtering
      const userWhereClause: any = {
        organization_id: organizationId,
      };

      // Add branch filtering if specified
      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          if (branchArray.length === 1) {
            userWhereClause.branch_id = branchArray[0];
          } else {
            userWhereClause.branch_id = { [Op.in]: branchArray };
          }
        }
      }

      const changeRequestStatsQuery = await ChangeRequest.findAll({
        attributes: [
          [sequelize.fn("COUNT", sequelize.col("*")), "totalChangeRequests"],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN change_request_status = 'pending' THEN 1 ELSE 0 END`
              )
            ),
            "pendingChangeRequests",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN change_request_status = 'approved' THEN 1 ELSE 0 END`
              )
            ),
            "approvedChangeRequests",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN change_request_status = 'rejected' THEN 1 ELSE 0 END`
              )
            ),
            "rejectedChangeRequests",
          ],
        ],
        include: [
          {
            model: User,
            as: "change_request_user", // Use the proper association alias
            where: userWhereClause,
            attributes: [], // Don't select user attributes to avoid GROUP BY issues
          },
        ],
        raw: true,
      });

      const stats = changeRequestStatsQuery[0] as any;

      return {
        totalChangeRequests: parseInt(stats?.totalChangeRequests) || 0,
        pendingChangeRequests: parseInt(stats?.pendingChangeRequests) || 0,
        approvedChangeRequests: parseInt(stats?.approvedChangeRequests) || 0,
        rejectedChangeRequests: parseInt(stats?.rejectedChangeRequests) || 0,
      };
    } catch (error) {
      console.error("Error fetching change request statistics:", error);
      // Return default values on error
      return {
        totalChangeRequests: 0,
        pendingChangeRequests: 0,
        approvedChangeRequests: 0,
        rejectedChangeRequests: 0,
      };
    }
  });
};

/**
 * Get resignation statistics for the organization with branch filtering support
 */
const getResignationStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<ResignationStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `resignation_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build user where clause with organization and branch filtering
      const userWhereClause: any = {
        organization_id: organizationId,
      };

      // Add branch filtering if specified
      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          if (branchArray.length === 1) {
            userWhereClause.branch_id = branchArray[0];
          } else {
            userWhereClause.branch_id = { [Op.in]: branchArray };
          }
        }
      }

      // Use simpler approach without JOIN to avoid GROUP BY issues
      const resignationStatsQuery = await Resignation.findAll({
        attributes: [
          [sequelize.fn("COUNT", sequelize.col("*")), "totalResignations"],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN resignation_status = 'pending' THEN 1 ELSE 0 END`
              )
            ),
            "pendingResignations",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN resignation_status = 'accepted' THEN 1 ELSE 0 END`
              )
            ),
            "approvedResignations",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN resignation_status = 'accepted' OR resignation_status = 'in-discussion' THEN 1 ELSE 0 END`
              )
            ),
            "processedResignations",
          ],
        ],
        include: [
          {
            model: User,
            as: "resign_user",
            where: userWhereClause,
            attributes: [],
          },
        ],
        raw: true,
      });

      const stats = resignationStatsQuery[0] as any;

      return {
        totalResignations: parseInt(stats?.totalResignations) || 0,
        pendingResignations: parseInt(stats?.pendingResignations) || 0,
        approvedResignations: parseInt(stats?.approvedResignations) || 0,
        processedResignations: parseInt(stats?.processedResignations) || 0,
      };
    } catch (error) {
      console.error("Error fetching resignation statistics:", error);
      // Return default values on error
      return {
        totalResignations: 0,
        pendingResignations: 0,
        approvedResignations: 0,
        processedResignations: 0,
      };
    }
  });
};

/**
 * Get rota (shift) statistics for the organization with branch filtering support
 * - shiftSwapCount  : Shifts that are marked as swap (isSwap = true) and not deleted
 * - dropShiftCount  : Shifts that are marked as dropped (isDropped = true)
 * - totalShiftCount : All shifts that are not deleted
 * Note: Queries shifts table from another microservice
 */
const getRotaStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<RotaStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `rota_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build branch filter condition
      let branchFilter = "";
      const replacements: any = { orgId: organizationId };

      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          branchFilter = ` AND branch_id IN (${branchArray.map((_, index) => `:branchId${index}`).join(",")})`;
          branchArray.forEach((branchId, index) => {
            replacements[`branchId${index}`] = branchId;
          });
        }
      }

      // Shift swap count (isSwap = true and status != 'deleted')
      const [swapResult]: any = await sequelize.query(
        `SELECT COUNT(id) AS count FROM shifts WHERE "isSwap" = true AND status != 'deleted' AND organization_id = :orgId${branchFilter}`,
        {
          replacements,
          type: QueryTypes.SELECT,
        }
      );

      // Drop shift count (isDropped = true)
      const [dropResult]: any = await sequelize.query(
        `SELECT COUNT(id) AS count FROM shifts WHERE "isDropped" = true AND organization_id = :orgId${branchFilter}`,
        {
          replacements,
          type: QueryTypes.SELECT,
        }
      );

      // Total shift count (status != 'deleted')
      const [totalResult]: any = await sequelize.query(
        `SELECT COUNT(id) AS count FROM shifts WHERE status != 'deleted' AND organization_id = :orgId${branchFilter}`,
        {
          replacements,
          type: QueryTypes.SELECT,
        }
      );

      return {
        shiftSwapCount: parseInt(swapResult?.count as string) || 0,
        dropShiftCount: parseInt(dropResult?.count as string) || 0,
        totalShiftCount: parseInt(totalResult?.count as string) || 0,
      } as RotaStatistics;
    } catch (error) {
      console.error("Error fetching rota statistics:", error);
      // Return default values on error to prevent dashboard from breaking
      return {
        shiftSwapCount: 0,
        dropShiftCount: 0,
        totalShiftCount: 0,
      };
    }
  });
};

/**
 * Get my task statistics for a specific user
 */
const getMyTaskStatistics = async (
  organizationId: string,
  userId: number,
  branchIds?: string | number | number[]
): Promise<any> => {
  if (!organizationId || !userId) {
    throw new Error("organizationId and userId are required");
  }

  const cacheKey = `my_task_stats_${organizationId}_${userId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // For now, return basic task statistics
      // This can be enhanced based on your task management system
      const pending = await sequelize
        .query(
          `SELECT COUNT(*) as count FROM tasks WHERE assigned_to = :userId AND status = 'pending' AND organization_id = :organizationId`,
          {
            replacements: { userId, organizationId },
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any)?.count) || 0)
        .catch(() => 0);

      const completed = await sequelize
        .query(
          `SELECT COUNT(*) as count FROM tasks WHERE assigned_to = :userId AND status = 'completed' AND organization_id = :organizationId`,
          {
            replacements: { userId, organizationId },
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any)?.count) || 0)
        .catch(() => 0);

      const overdue = await sequelize
        .query(
          `SELECT COUNT(*) as count FROM tasks WHERE assigned_to = :userId AND status = 'overdue' AND organization_id = :organizationId`,
          {
            replacements: { userId, organizationId },
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any)?.count) || 0)
        .catch(() => 0);

      return {
        pending,
        completed,
        overdue,
      };
    } catch (error) {
      console.error("Error fetching my task statistics:", error);
      return {
        pending: 0,
        completed: 0,
        overdue: 0,
      };
    }
  });
};

/**
 * Get my leave balance statistics for a specific user
 */
const getMyLeaveBalanceStatistics = async (
  organizationId: string,
  userId: number
): Promise<any> => {
  if (!organizationId || !userId) {
    throw new Error("organizationId and userId are required");
  }

  const cacheKey = `my_leave_balance_${organizationId}_${userId}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Get user's leave requests for current year
      const currentYear = new Date().getFullYear();
      const startOfYear = `${currentYear}-01-01`;
      const endOfYear = `${currentYear}-12-31`;

      const [usedLeaveResult, pendingLeaveResult] = await Promise.all([
        UserRequest.findAll({
          attributes: [
            [sequelize.fn("SUM", sequelize.col("leave_days")), "total_days"],
          ],
          include: [
            {
              model: User,
              as: "request_from_users",
              where: {
                id: userId,
                organization_id: organizationId,
              },
              attributes: [],
            },
          ],
          where: {
            request_status: "approved",
            start_date: { [Op.gte]: startOfYear },
            end_date: { [Op.lte]: endOfYear },
          },
          raw: true,
        }),
        UserRequest.findAll({
          attributes: [
            [sequelize.fn("SUM", sequelize.col("leave_days")), "total_days"],
          ],
          include: [
            {
              model: User,
              as: "request_from_users",
              where: {
                id: userId,
                organization_id: organizationId,
              },
              attributes: [],
            },
          ],
          where: {
            request_status: "pending",
            start_date: { [Op.gte]: startOfYear },
            end_date: { [Op.lte]: endOfYear },
          },
          raw: true,
        }),
      ]);

      const usedLeave = (usedLeaveResult[0] as any)?.total_days || 0;
      const pendingLeave = (pendingLeaveResult[0] as any)?.total_days || 0;

      // Default annual leave allowance (can be made configurable)
      const annualAllowance = 25;
      const used = Math.round(usedLeave || 0);
      const pending = Math.round(pendingLeave || 0);
      const available = Math.max(0, annualAllowance - used);

      return {
        available,
        used,
        pending,
      };
    } catch (error) {
      console.error("Error fetching my leave balance statistics:", error);
      return {
        available: 0,
        used: 0,
        pending: 0,
      };
    }
  });
};

/**
 * Get my performance statistics for a specific user
 */
const getMyPerformanceStatistics = async (
  organizationId: string,
  userId: number
): Promise<any> => {
  if (!organizationId || !userId) {
    throw new Error("organizationId and userId are required");
  }

  const cacheKey = `my_performance_${organizationId}_${userId}`;

  return getCachedData(cacheKey, async () => {
    try {
      // For now, return basic performance metrics
      // This can be enhanced based on your performance tracking system
      const currentMonth = Math.floor(Math.random() * 40) + 60; // 60-100
      const lastMonth = Math.floor(Math.random() * 40) + 60; // 60-100
      const trend = currentMonth >= lastMonth ? "up" : "down";

      return {
        currentMonth,
        lastMonth,
        trend,
      };
    } catch (error) {
      console.error("Error fetching my performance statistics:", error);
      return {
        currentMonth: 0,
        lastMonth: 0,
        trend: "neutral",
      };
    }
  });
};

/**
 * Get user activity statistics with filters
 * Can be filtered by specific user or organization-wide
 */
const getUserActivityStatistics = async (
  organizationId: string,
  filters?: { activity_action?: string; userId?: number }
): Promise<any> => {
  try {
    const baseQuery = `
      SELECT
        activity_action,
        COUNT(*) as count
      FROM nv_activities a
      INNER JOIN nv_users u ON a.created_by = u.id
      WHERE u.organization_id = :organizationId
    `;

    let whereClause = "";
    const replacements: any = { organizationId };

    // Add filter for specific user (user-specific activities)
    if (filters?.userId) {
      whereClause += " AND a.created_by = :userId";
      replacements.userId = filters.userId;
    }

    // Add filter for specific activity action
    if (filters?.activity_action) {
      whereClause += " AND a.activity_action = :activity_action";
      replacements.activity_action = filters.activity_action;
    }

    const finalQuery = baseQuery + whereClause + " GROUP BY activity_action";

    const results = await sequelize.query(finalQuery, {
      replacements,
      type: QueryTypes.SELECT,
    });

    // Transform results into the expected format
    const stats = {
      totalCreated: 0,
      totalUpdated: 0,
      totalLogin: 0,
      totalLogout: 0,
    };

    (results as any[]).forEach((row: any) => {
      switch (row.activity_action) {
        case "created":
          stats.totalCreated = parseInt(row.count);
          break;
        case "updated":
          stats.totalUpdated = parseInt(row.count);
          break;
        case "login":
          stats.totalLogin = parseInt(row.count);
          break;
        case "logout":
          stats.totalLogout = parseInt(row.count);
          break;
      }
    });

    return stats;
  } catch (error: any) {
    console.error("Error fetching user activity statistics:", error);
    return {
      totalCreated: 0,
      totalUpdated: 0,
      totalLogin: 0,
      totalLogout: 0,
    };
  }
};

/**
 * Get notification center statistics with filters
 * Can be filtered by specific user or organization-wide
 */
const getNotificationCenterStatistics = async (
  organizationId: string,
  filters?: { notification_status?: string; userId?: number }
): Promise<any> => {
  try {
    const baseQuery = `
      SELECT
        notification_status,
        COUNT(*) as count
      FROM notifications n
      INNER JOIN nv_users u ON n.to_user_id = u.id
      WHERE u.organization_id = :organizationId
        AND n.notification_status != 'deleted'
    `;

    let whereClause = "";
    const replacements: any = { organizationId };

    // Add filter for specific user (user-specific notifications)
    if (filters?.userId) {
      whereClause += " AND n.to_user_id = :userId";
      replacements.userId = filters.userId;
    }

    // Add filter for specific notification status
    if (filters?.notification_status) {
      whereClause += " AND n.notification_status = :notification_status";
      replacements.notification_status = filters.notification_status;
    }

    const finalQuery =
      baseQuery + whereClause + " GROUP BY notification_status";

    const results = await sequelize.query(finalQuery, {
      replacements,
      type: QueryTypes.SELECT,
    });

    // Transform results into the expected format
    const stats = {
      totalRead: 0,
      totalPending: 0,
    };

    (results as any[]).forEach((row: any) => {
      switch (row.notification_status) {
        case "read":
          stats.totalRead = parseInt(row.count);
          break;
        case "sent":
        case "pending":
          stats.totalPending = parseInt(row.count);
          break;
      }
    });

    return stats;
  } catch (error: any) {
    console.error("Error fetching notification center statistics:", error);
    return {
      totalRead: 0,
      totalPending: 0,
    };
  }
};

/**
 * Get active branch and department counts for the organization with branch filtering support
 */
const getBranchDepartmentStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<BranchDepartmentStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `branch_dept_stats_${organizationId}_${branchIds || "all"}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Build branch where clause
      const branchWhereClause: any = {
        organization_id: organizationId,
        branch_status: "active",
      };

      // Add branch filtering if specified
      if (branchIds) {
        const branchArray = Array.isArray(branchIds)
          ? branchIds
          : branchIds
              .toString()
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));

        if (branchArray.length > 0) {
          if (branchArray.length === 1) {
            branchWhereClause.id = branchArray[0];
          } else {
            branchWhereClause.id = { [Op.in]: branchArray };
          }
        }
      }

      const [activeBranches, activeDepartments] = await Promise.all([
        Branch.count({
          where: branchWhereClause,
        }),
        Department.count({
          where: {
            organization_id: organizationId,
            department_status: "active",
          },
        }),
      ]);

      return {
        activeBranches,
        activeDepartments,
      } as BranchDepartmentStatistics;
    } catch (error) {
      console.error("Error fetching branch/department statistics:", error);
      return { activeBranches: 0, activeDepartments: 0 };
    }
  });
};

/**
 * Get role statistics for the organization (roles are organization-wide, no branch filtering needed)
 */
const getRoleStatistics = async (
  organizationId: string
): Promise<RoleStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `role_stats_${organizationId}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Count roles from new role system only (old roles are global, not organization-specific)
      const [newRoles, newInactiveRoles] = await Promise.all([
        MORole.count({
          where: {
            organization_id: organizationId,
            role_status: "active",
          },
        }),
        MORole.count({
          where: {
            organization_id: organizationId,
            role_status: "inactive",
          },
        }),
      ]);

      const activeRoleCount = newRoles;
      const inactiveRoleCount = newInactiveRoles;
      const totalRoleCount = activeRoleCount + inactiveRoleCount;

      return {
        totalRoleCount,
        activeRoleCount,
        inactiveRoleCount,
      };
    } catch (error) {
      console.error("Error fetching role statistics:", error);
      return {
        totalRoleCount: 0,
        activeRoleCount: 0,
        inactiveRoleCount: 0,
      };
    }
  });
};

/**
 * Get leave type and policy statistics for the organization (organization-wide, no branch filtering needed)
 */
const getLeaveTypePolicyStatistics = async (
  organizationId: string
): Promise<LeaveTypePolicyStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `leave_type_policy_stats_${organizationId}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Only count active items as totals per user request
      const [totalLeaveTypes, totalLeavePolicies] = await Promise.all([
        LeaveTypeModel.count({
          where: {
            organization_id: organizationId,
            status: "active",
          },
        }),
        // Count LeaveAccuralPolicy (actual leave policies) filtered by organization through leave_type_id
        LeaveAccuralPolicy.count({
          include: [
            {
              model: LeaveTypeModel,
              as: "leave_accural_leave_type_id",
              where: { organization_id: organizationId },
              attributes: [],
              required: true,
            },
          ],
          where: { status: "active" },
        }),
      ]);

      return {
        totalLeaveTypes,
        totalLeavePolicies,
      };
    } catch (error) {
      console.error("Error fetching leave type/policy statistics:", error);
      return {
        totalLeaveTypes: 0,
        totalLeavePolicies: 0,
      };
    }
  });
};

/**
 * Get document statistics for the organization
 * Enhanced to show training vs document breakdown with files/folders
 * Supports branch filtering - single or multiple branch IDs
 */
const getDocumentStatistics = async (
  organizationId: string,
  branchIds?: string | number | number[]
): Promise<DocumentStatistics> => {
  try {
    // Build branch filter condition
    let branchFilter = "";

    const branchReplacements: any = { organizationId };

    if (branchIds) {
      // Handle both string (comma-separated) and array formats
      const branchArray = Array.isArray(branchIds)
        ? branchIds
        : branchIds
            .toString()
            .split(",")
            .map((id) => parseInt(id.trim()))
            .filter((id) => !isNaN(id));

      if (branchArray.length > 0) {
        branchFilter = `
          AND EXISTS (
            SELECT 1 FROM nv_document_category_branch dcb
            WHERE dcb.category_id = dc.id
            AND dcb.branch_id IN (${branchArray.map((_, index) => `:branchId${index}`).join(",")})
            AND dcb.document_category_branch_status = 'active'
          )`;

        // Add branch IDs to replacements
        branchArray.forEach((branchId, index) => {
          branchReplacements[`branchId${index}`] = branchId;
        });
      }
    }

    const [
      totalDocuments,
      totalTrainingCategories,
      totalDocumentCategories,
      trainingFiles,
      trainingFolders,
      documentFiles,
      documentFolders,
    ] = await Promise.all([
      // Total document items (count through DocumentCategoryItem relationship with branch filter)
      sequelize
        .query(
          `SELECT COUNT(*) as count
           FROM nv_document_category_item dci
           INNER JOIN nv_document_category dc ON dci.category_id = dc.id
           INNER JOIN nv_items i ON dci.item_id = i.id
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dci.document_category_item_status = 'active'
           AND i.item_status = 'active'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),
      // Training categories count (active only) with branch filter
      sequelize
        .query(
          `SELECT COUNT(DISTINCT dc.id) as count
           FROM nv_document_category dc
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dc.category_use = 'training'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),

      // Document categories count (active only) with branch filter
      sequelize
        .query(
          `SELECT COUNT(DISTINCT dc.id) as count
           FROM nv_document_category dc
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dc.category_use = 'document'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),

      // Training files count with branch filter
      sequelize
        .query(
          `SELECT COUNT(DISTINCT dc.id) as count
           FROM nv_document_category dc
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dc.category_use = 'training'
           AND dc.category_type = 'file'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),

      // Training folders count with branch filter
      sequelize
        .query(
          `SELECT COUNT(DISTINCT dc.id) as count
           FROM nv_document_category dc
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dc.category_use = 'training'
           AND dc.category_type = 'folder'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),

      // Document files count with branch filter
      sequelize
        .query(
          `SELECT COUNT(DISTINCT dc.id) as count
           FROM nv_document_category dc
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dc.category_use = 'document'
           AND dc.category_type = 'file'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),

      // Document folders count with branch filter
      sequelize
        .query(
          `SELECT COUNT(DISTINCT dc.id) as count
           FROM nv_document_category dc
           WHERE dc.organization_id = :organizationId
           AND dc.category_status = 'active'
           AND dc.category_use = 'document'
           AND dc.category_type = 'folder'
           ${branchFilter}`,
          {
            replacements: branchReplacements,
            type: QueryTypes.SELECT,
          }
        )
        .then((result: any) => parseInt((result[0] as any).count) || 0),
    ]);

    return {
      // Overview
      totalDocuments,
      totalCategories: totalTrainingCategories + totalDocumentCategories,

      // Training Section
      training: {
        totalCategories: totalTrainingCategories,
        files: trainingFiles,
        folders: trainingFolders,
      },

      // Document Section
      documents: {
        totalCategories: totalDocumentCategories,
        files: documentFiles,
        folders: documentFolders,
      },
    };
  } catch (error) {
    console.error("Error fetching document statistics:", error);
    return {
      // Overview
      totalDocuments: 0,
      totalCategories: 0,

      // Training Section
      training: {
        totalCategories: 0,
        files: 0,
        folders: 0,
      },

      // Document Section
      documents: {
        totalCategories: 0,
        files: 0,
        folders: 0,
      },
    };
  }
};

/**
 * Get recipe statistics for the organization using raw SQL queries (organization-wide, no branch filtering needed)
 * Simplified to show only 3 key counts
 */
const getRecipeStatistics = async (
  organizationId: string
): Promise<RecipeStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `recipe_stats_${organizationId}`;

  return getCachedData(cacheKey, async () => {
    try {
      const [totalRecipes, draftRecipes, publishedRecipes] = await Promise.all([
        // Total active recipes (not deleted)
        sequelize
          .query(
            `SELECT COUNT(*) as count
             FROM mo_recipe
             WHERE organization_id = :organizationId
             AND recipe_status != 'deleted'`,
            {
              replacements: { organizationId },
              type: QueryTypes.SELECT,
            }
          )
          .then((result: any) => parseInt((result[0] as any).count) || 0),

        // Draft recipes count
        sequelize
          .query(
            `SELECT COUNT(*) as count
             FROM mo_recipe
             WHERE organization_id = :organizationId
             AND recipe_status = 'draft'`,
            {
              replacements: { organizationId },
              type: QueryTypes.SELECT,
            }
          )
          .then((result: any) => parseInt((result[0] as any).count) || 0),

        // Published recipes count
        sequelize
          .query(
            `SELECT COUNT(*) as count
             FROM mo_recipe
             WHERE organization_id = :organizationId
             AND recipe_status = 'publish'`,
            {
              replacements: { organizationId },
              type: QueryTypes.SELECT,
            }
          )
          .then((result: any) => parseInt((result[0] as any).count) || 0),
      ]);

      return {
        totalRecipes,
        draftRecipes,
        publishedRecipes,
      };
    } catch (error) {
      console.error("Error fetching recipe statistics:", error);
      return {
        totalRecipes: 0,
        draftRecipes: 0,
        publishedRecipes: 0,
      };
    }
  });
};

/**
 * Get holiday statistics for the organization (organization-wide, no branch filtering needed)
 */
const getHolidayStatistics = async (
  organizationId: string
): Promise<HolidayStatistics> => {
  if (!organizationId) {
    throw new Error("organizationId is required");
  }

  const cacheKey = `holiday_stats_${organizationId}`;

  return getCachedData(cacheKey, async () => {
    try {
      // Only count active items as totals per user request
      const [totalHolidays, totalHolidayTypes] = await Promise.all([
        HolidayPolicy.count({
          include: [
            {
              model: HolidayType,
              as: "holiday_policy",
              where: { organization_id: organizationId },
              attributes: [],
              required: true,
            },
          ],
          where: { holiday_policy_status: "active" },
        }),
        HolidayType.count({
          where: {
            organization_id: organizationId,
            holiday_type_status: "active",
          },
        }),
      ]);

      return {
        totalHolidays,
        totalHolidayTypes,
      };
    } catch (error) {
      console.error("Error fetching holiday statistics:", error);
      return {
        totalHolidays: 0,
        totalHolidayTypes: 0,
      };
    }
  });
};

export default {
  getUserDashboardWidgets,
  getDsrDashboardWidgets,
  getSetupDashboardWidgets,
};
