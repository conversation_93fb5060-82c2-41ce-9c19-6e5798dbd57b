import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { payment_type_usage, PaymentType, payment_type_status as paymentTypeStatus } from "../models/PaymentType";
import { addSpacesBeforeCapitals, createNotification, generateReport, generateReportNew, getColumnValue, getColumnValueForGeneral, getGeneralSettingObj, getOrganizationLogo, getUserFullName, getWeekCount, readHTMLFile, validateModulePermission } from "../helper/common";
import { EMAIL_ADDRESS, NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, ROLE_CONSTANT, REDIRECTION_TYPE, ROLE_PERMISSIONS } from "../helper/constant";
import { DsrDetail, dsr_detail_status } from "../models/DsrDetail";
import moment from "moment";
import { Role } from "../models/Role";
import { DsrItem, dsr_item_status } from "../models/DsrItem";
import { Branch, branch_status } from "../models/Branch";
import { Op } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { User, user_status } from "../models/User";
import { sequelize } from "../models";
import { DsrRequest, dsr_request_status, dsr_request_status as request_status } from "../models/DsrRequest";
import { DsrItemRequest, dsr_request_item_status } from "../models/DsrItemRequest";
import { Activity } from "../models/Activity";
import path from "path";
import { DSRCONSTANT } from "../helper/constant";
import handlebars from "handlebars";
import { PaymentTypeCategory, payment_type_category_status as paymentTypeCategoryStatus, payment_type_category_pattern as paymentTypeCategoryPattern } from "../models/PaymentTypeCategory";
import { PaymentTypeCategoryBranch, payment_type_category_branch_status as paymentTypeCategoryBranchStatus } from "../models/PaymentTypeCategoryBranch";
import { wsr_detail_status, WsrDetail } from "../models/WsrDetail";
import { wsr_item_status, WsrItem } from "../models/WsrItem";
import { wsr_request_status, WsrRequest } from "../models/WsrRequest";
import { wsr_request_item_status, WsrItemRequest } from "../models/WsrItemRequest";
import { PaymentTypeRemark } from "../models/PaymentTypeRemark";
import { generateFile } from "../helper/fileGeneration.service";

/**
 * Get dsr Payment type 
 * @param req 
 * @param res 
 * @returns 
 */
const getDsrPaymentType = async (req: Request, res: Response) => {
    try {
        // Extracting parameters from request
        const { branch_id }: any = req.params;
        const { payment_type }: any = req.query;

        // Define base query conditions
        const whereObj: any = {
            payment_type_status: paymentTypeStatus.ACTIVE,
            organization_id: req.user.organization_id
        };

        // Dynamically set conditions based on the 'payment_type'
        switch (payment_type) {
            case 'dsr':
                whereObj.payment_type_usage = { [Op.in]: [payment_type_usage.COLLECTION, payment_type_usage.OTHER] };
                whereObj.has_weekly_use = 0;
                break;
            case 'wsr':
                whereObj.payment_type_usage = { [Op.in]: [payment_type_usage.COLLECTION, payment_type_usage.OTHER] };
                whereObj.has_weekly_use = 1;
                break;
            case 'payroll':
                whereObj.payment_type_usage = { [Op.in]: [payment_type_usage.EXPENSE] };
                break;
        }

        // Fetch Payment Type Details
        let getPaymentTypeDetails: any = await PaymentType.findAll({
            attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency'],
            include: [
                {
                    model: PaymentTypeCategory,
                    as: "payment_type_category",
                    attributes: [['id', 'payment_type_category_id'], 'payment_type_category_remarks', 'payment_type_category_title', 'payment_type_category_status', 'payment_type_category_pattern', 'payment_type_category_order',
                    [sequelize.literal(`(SELECT id 
                                FROM nv_payment_type_category_branch 
                                WHERE branch_id = ${branch_id} 
                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                AND parent_id is null 
                                AND has_default_active = 1
                                AND payment_type_category_id = payment_type_category.id
                    )`), 'payment_type_category_branch_id']],
                    where: {
                        payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                        id: {
                            [Op.in]: sequelize.literal(`(
                                SELECT payment_type_category_id 
                                FROM nv_payment_type_category_branch 
                                WHERE branch_id = ${branch_id} 
                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                AND parent_id is null 
                                AND has_default_active = 1
                            )`)
                        }
                    },
                }
            ],
            where: whereObj,
            order: [
                ['payment_type_order', 'ASC'],
                [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
            ],
        })

        // If payment types exist, process the categories and fetch their branches
        if (getPaymentTypeDetails.length > 0) {
            // Convert data to JSON format for easier manipulation
            getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

            // Loop through each payment type's category
            for (const paymentType of getPaymentTypeDetails) {
                paymentType.payment_type_remark = null
                if (paymentType?.payment_type_category?.length > 0) {
                    for (const category of paymentType.payment_type_category) {

                        // Fetch category branches for each category
                        const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                            attributes: [
                                ['id', 'reference_id'],
                                [sequelize.literal(`
                                    (SELECT nv_payment_type_category_value.field_value 
                                    FROM nv_payment_type_category_value 
                                    INNER JOIN nv_payment_type_category_field 
                                    ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                    WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                    -- AND nv_payment_type_category_field.field_type = 'string' 
                                    AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                    ORDER BY nv_payment_type_category_value.createdAt ASC 
                                    LIMIT 1
                                )`), 'first_field_value']
                            ],
                            where: {
                                branch_id,
                                parent_id: category.payment_type_category_branch_id,
                                payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                has_default_active: 1
                            },
                            order: [['payment_type_category_branch_order', 'ASC']],
                        });

                        // Assign found branch value to category
                        category.categoryBranchValue = findCategoryBranch;
                    }
                }
            }
        }

        // Return the final response with the payment type details
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getPaymentTypeDetails || [],
            vat_per_data: global.config.VAT_PER_DATA
        });

    } catch (error) {
        console.error("Error in getDsrPaymentType:", error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const addDsrDetail = async (req: Request, res: Response) => {
    try {
        let findRole: any;

        // Determine the user role based on the platform type
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.web_user_active_role_id ? req.user.web_user_active_role_id : req.user.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && req.user.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.user_active_role_id }, raw: true });
        }

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'dsr', // DSR module slug
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
        //         ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
        //         ROLE_CONSTANT.DIRECTOR
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = findRole && [
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER,
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
            ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
            ROLE_CONSTANT.DIRECTOR
        ].includes(findRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        // Destructure request body parameters
        const { dsr_date, branch_id, data = [], current_datetime, dsr_amount_total = {} } = req.body;
        const collectionDateMoment = moment(dsr_date).startOf('day'); // Start of the collection date at midnight
        const previousDate = collectionDateMoment.clone().subtract(1, 'day').format("YYYY-MM-DD"); // Previous date
        const endDate = collectionDateMoment.clone().add(1, 'day').hour(17).minute(30).second(0); // Next day at 5:30 PM
        const todayEndDate = collectionDateMoment.clone().hour(17).minute(30).second(0); // Today at 5:30 PM
        const currentMoment = moment(current_datetime); // Current time

        // Check if the specified branch exists and is not deleted
        const findBranch = await Branch.findOne({ attributes: ['id', 'branch_name'], where: { id: branch_id, organization_id: req.user.organization_id, branch_status: { [Op.not]: branch_status.DELETED } }, raw: true });
        if (!findBranch) {
            return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
        }

        // Check if a DSR detail for the specified date already exists
        const getDsrDetail = await DsrDetail.findOne({ attributes: ['id'], where: { branch_id: branch_id, dsr_date, dsr_detail_status: dsr_detail_status.ACTIVE }, raw: true });
        if (getDsrDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__("DSR_ALREADY_ADDED") });
        }

        // Role-specific logic for adding DSR details
        if (findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER) {
            // Check if the current time is not within the allowed range
            if (!currentMoment.isBetween(collectionDateMoment, endDate, null, '[]')) {

                return res.status(StatusCodes.EXPECTATION_FAILED).json({
                    status: false,
                    message: (currentMoment.clone().subtract(1, 'day').format("YYYY-MM-DD") != collectionDateMoment.format("YYYY-MM-DD")) ? res.__("FAIL_CANNOT_ADD_BEFORE") : res.__("FAIL_CANNOT_ADD_AFTER")
                });
            }

            // Check if today’s time is between start and todayEndDate (5:30 PM)
            if (currentMoment.isBetween(collectionDateMoment, todayEndDate, null, '[]')) {
                // Check if yesterday's DSR detail exists
                const getPreviousDsrDetail = await DsrDetail.findOne({
                    where: {
                        branch_id: branch_id,
                        dsr_date: previousDate,
                        dsr_detail_status: dsr_detail_status.ACTIVE
                    }, raw: true
                });

                if (!getPreviousDsrDetail) {
                    return res.status(StatusCodes.BAD_REQUEST).json({
                        status: false,
                        message: res.__("ERROR_FIRST_ADD_YESTERDAY_DSR")
                    });
                }
            }
        }

        // Create a new DSR detail entry
        const dsrDetail = await DsrDetail.setHeaders(req).create({
            user_id: req.user.id,
            branch_id: branch_id,
            dsr_date,
            dsr_detail_status: dsr_detail_status.ACTIVE,
            dsr_amount_total: JSON.stringify(dsr_amount_total),
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);


        // If DSR detail is created successfully, handle the associated data items
        if (dsrDetail) {
            if (data && data.length > 0) {
                for (const item of data) {
                    const findDsrRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: dsrDetail.id, payment_type_id: item.id }, raw: true });
                    if (findDsrRemark) {
                        await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: dsrDetail.id, payment_type_id: item.id } });
                    } else {
                        await PaymentTypeRemark.setHeaders(req).create({
                            detail_id: dsrDetail.id,
                            payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                            payment_type_remark: item.payment_type_remark,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any);
                    }
                    // Check if there are payment type categories in the item
                    if (item?.payment_type_category.length > 0) {
                        for (const category of item.payment_type_category) {
                            // Handle categories with multiple options
                            if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                for (const option of category.categoryBranchValue) {
                                    // Check if the payment type category branch exists
                                    if (option?.dsr_amount) {
                                        const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                            attributes: ['id'],
                                            where: {
                                                id: option?.reference_id,
                                                branch_id: branch_id,
                                                payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                            }, raw: true
                                        });
                                        if (findDocumentCategoryBranch) {
                                            // Check if the DSR item already exists
                                            const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { dsr_detail_id: dsrDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                            if (!findDsrItem) {
                                                // Create a new DSR item if it doesn't exist
                                                await DsrItem.setHeaders(req).create({
                                                    dsr_detail_id: dsrDetail.id,
                                                    payment_type_category_id: category.payment_type_category_id,
                                                    dsr_amount: option.dsr_amount,
                                                    reference_id: option.reference_id,
                                                    dsr_item_status: dsr_item_status.ACTIVE,
                                                    created_by: req.user.id,
                                                    updated_by: req.user.id
                                                } as any);
                                            }
                                        }
                                    }
                                }
                            } else {
                                if (category?.dsr_amount) {
                                    // Handle single option categories
                                    const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { dsr_detail_id: dsrDetail.id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                    if (!findDsrItem) {
                                        // Create a new DSR item if it doesn't exist
                                        await DsrItem.setHeaders(req).create({
                                            dsr_detail_id: dsrDetail.id,
                                            payment_type_category_id: category.payment_type_category_id,
                                            dsr_amount: category.dsr_amount,
                                            dsr_item_status: dsr_item_status.ACTIVE,
                                            created_by: req.user.id,
                                            updated_by: req.user.id
                                        } as any);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Return success response after DSR detail and items are added
            return res.status(StatusCodes.OK).json({ status: true, message: res.__("DSR_ADDED_SUCCESSFULLY") });
        } else {
            // Return failure response if DSR detail could not be added
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FAIL_TO_ADD_DSR") });
        }
    } catch (error) {
        // Handle any errors that occur during the process
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const updateDsrDetail = async (req: Request, res: Response) => {
    try {
        let findRole: any; // Variable to hold the user's role

        // Determine the user's role based on the platform type
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.web_user_active_role_id ? req.user.web_user_active_role_id : req.user.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && req.user.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.user_active_role_id }, raw: true });
        }

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'dsr', // DSR module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
        //         ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
        //         ROLE_CONSTANT.DIRECTOR
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = findRole && [
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER,
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
            ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
            ROLE_CONSTANT.DIRECTOR
        ].includes(findRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        const { data = [], current_datetime, dsr_amount_total, dsr_date, old_dsr_amount_total } = req.body; // Extract data from request body
        const { dsr_detail_id } = req.params; // Extract the DSR detail ID from request parameters

        // Retrieve the DSR detail from the database
        const getDsrDetail: any = await DsrDetail.findOne({ attributes: ['id', 'dsr_date', 'branch_id'], where: { id: dsr_detail_id, dsr_detail_status: dsr_detail_status.ACTIVE }, raw: true });

        if (getDsrDetail) {
            if (findRole?.role_name == ROLE_CONSTANT.SUPER_ADMIN || findRole?.role_name == ROLE_CONSTANT.ADMIN || findRole?.role_name == ROLE_CONSTANT.DIRECTOR || findRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
                const findDsrBasedDate = await DsrDetail.findOne({ attributes: ['id'], where: { dsr_date: dsr_date, dsr_detail_status: dsr_detail_status.ACTIVE, branch_id: getDsrDetail.branch_id }, raw: true });
                if (findDsrBasedDate) {
                    if (getDsrDetail.id != findDsrBasedDate?.id) {
                        return res.status(StatusCodes.BAD_REQUEST).json({
                            status: false,
                            message: res.__("DSR_ALREADY_EXISTS"),
                        })
                    }
                }
            }
            const getFullName = await getUserFullName(req.user.id); // Retrieve the full name of the user

            const collectionDateMoment = moment(getDsrDetail.dsr_date).startOf('day'); // Start of the collection date at midnight
            const endDate = collectionDateMoment.clone().add(1, 'day').hour(17).minute(30).second(0); // Next day at 5:30 PM
            const currentMoment = moment(current_datetime); // Current time

            // Check if the collection date is yesterday and if the current time is past 5:30 PM today
            if (findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER) {
                //  if (!(currentMoment < moment(today8AM)) && collectionDateMoment < moment(currentDate)) {
                if (!currentMoment.isBetween(collectionDateMoment, endDate, null, '[]')) {
                    // Check for existing DSR requests
                    const findRequest = await DsrRequest.findOne({ attributes: ['id'], where: { dsr_detail_id: getDsrDetail.id, dsr_request_status: dsr_request_status.PENDING }, raw: true });
                    if (findRequest) {
                        return res
                            .status(StatusCodes.EXPECTATION_FAILED)
                            .json({ status: false, message: res.__("DSR_UPDATED_REQUEST_EXIST") }); // Return error if a pending request exists
                    }

                    // Create a new DSR request
                    const dsrRequest = await DsrRequest.setHeaders(req).create({ dsr_detail_id: getDsrDetail.id, user_id: req.user.id, dsr_amount_total: JSON.stringify(dsr_amount_total), dsr_request_status: request_status.PENDING, created_by: req.user.id, updated_by: req.user.id, old_dsr_amount_total: JSON.stringify(old_dsr_amount_total) } as any);

                    if (dsrRequest) {
                        if (data && data.length > 0) {
                            // Loop through data and handle payment type categories
                            for (const item of data) {
                                const findDsrRemark = await PaymentTypeRemark.findOne({ where: { detail_id: dsr_detail_id, payment_type_id: item.id }, raw: true });
                                if (findDsrRemark) {
                                    await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: dsr_detail_id, payment_type_id: item.id } });
                                } else {
                                    await PaymentTypeRemark.setHeaders(req).create({
                                        detail_id: dsr_detail_id,
                                        payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                                        payment_type_remark: item.payment_type_remark,
                                        created_by: req.user.id,
                                        updated_by: req.user.id
                                    } as any);
                                }
                                if (item?.payment_type_category.length > 0) {
                                    for (const category of item.payment_type_category) {
                                        if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                            // Handle multiple payment types
                                            for (const option of category.categoryBranchValue) {
                                                if (option.dsr_amount || option.old_dsr_amount) {
                                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                                        attributes: ['id'],
                                                        where: {
                                                            id: option?.reference_id, branch_id: getDsrDetail.branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                                        }, raw: true
                                                    });
                                                    if (findDocumentCategoryBranch) {
                                                        option.dsr_amount = option.dsr_amount ? option.dsr_amount : null;
                                                        // Check for existing DSR item request
                                                        const findDsrItem = await DsrItemRequest.findOne({ attributes: ['id'], where: { dsr_request_detail_id: dsrRequest.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                                        if (!findDsrItem) {
                                                            // Create new DSR item request if it doesn't exist
                                                            await DsrItemRequest.setHeaders(req).create({ dsr_request_detail_id: dsrRequest.id, payment_type_category_id: category.payment_type_category_id, dsr_amount: option.dsr_amount, reference_id: option.reference_id, dsr_request_item_status: dsr_request_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id, old_dsr_amount: option.old_dsr_amount } as any);
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            // Handle single payment type
                                            if (category.dsr_amount || category.old_dsr_amount) {
                                                const findDsrItemRequest = await DsrItemRequest.findOne({ attributes: ['id'], where: { dsr_request_detail_id: dsrRequest.id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                                if (!findDsrItemRequest) {
                                                    category.dsr_amount = category.dsr_amount ? category.dsr_amount : null;
                                                    // Create new DSR item request if it doesn't exist
                                                    await DsrItemRequest.setHeaders(req).create({ dsr_request_detail_id: dsrRequest.id, payment_type_category_id: category.payment_type_category_id, dsr_amount: category.dsr_amount, dsr_request_item_status: dsr_request_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id, old_dsr_amount: category.old_dsr_amount } as any);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // Find users with specific roles to notify
                        const findUsers =
                            (await User.findAll({
                                attributes: ['id', 'appToken', 'webAppToken'],
                                where: {
                                    user_status: {
                                        [Op.not]: [
                                            user_status.DELETED,
                                            user_status.PENDING,
                                            user_status.CANCELLED,
                                        ],
                                    },
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(
                                                `(SELECT nv_user_roles.user_id FROM nv_user_roles WHERE nv_user_roles.role_id IN (SELECT id FROM nv_roles WHERE role_name IN ('${ROLE_CONSTANT.SUPER_ADMIN}','${ROLE_CONSTANT.ADMIN}','${ROLE_CONSTANT.DIRECTOR}','${ROLE_CONSTANT.ACCOUNTANT}')))`
                                            ),
                                        ],
                                    },
                                    organization_id: req.user.organization_id
                                }, raw: true,
                                group: ['id']
                            })) || [];
                        const findBranch = await Branch.findOne({ attributes: ['id', 'branch_name'], where: { id: getDsrDetail.branch_id, organization_id: req.user.organization_id }, raw: true }); // Retrieve the branch details

                        // Create notification for users
                        await createNotification(findUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.DSR_UPDATE_REQUEST.content(getFullName, findBranch?.branch_name, moment(getDsrDetail?.dsr_date).format("DD/MM/YYYY")), NOTIFICATIONCONSTANT.DSR_UPDATE_REQUEST.heading, REDIRECTION_TYPE.DSR, getDsrDetail.id, { dsr_id: getDsrDetail.id });
                        return res
                            .status(StatusCodes.OK)
                            .json({ status: true, message: res.__("DSR_REQUEST_ADDED") }); // Return success response
                    } else {
                        return res
                            .status(StatusCodes.EXPECTATION_FAILED)
                            .json({ status: false, message: res.__("FAIL_TO_ADD_DSR_REQUEST") }); // Return error if request addition fails
                    }
                }
            }
            // Handle updates for DSR items
            if (data && data.length > 0) {
                const dsrItemIds: any = []; // Array to hold DSR item IDs
                for (const item of data) {
                    const findDsrRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: dsr_detail_id, payment_type_id: item.id }, raw: true });
                    if (findDsrRemark) {
                        await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: dsr_detail_id, payment_type_id: item.id } });
                    } else {
                        await PaymentTypeRemark.setHeaders(req).create({
                            detail_id: dsr_detail_id,
                            payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                            payment_type_remark: item.payment_type_remark,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any);
                    }
                    if (item?.payment_type_category.length > 0) {
                        for (const category of item.payment_type_category) {
                            if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                // Handle multiple payment types
                                for (const option of category.categoryBranchValue) {
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: option?.reference_id, branch_id: getDsrDetail.branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    });
                                    if (findDocumentCategoryBranch) {
                                        if (option.dsr_item_id) {
                                            if (option?.dsr_amount) {
                                                // Update existing DSR item if ID is provided
                                                const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { id: option.dsr_item_id }, raw: true });
                                                if (findDsrItem) {
                                                    dsrItemIds.push(findDsrItem.id);
                                                    await DsrItem.setHeaders(req).update({ dsr_amount: option.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: option.dsr_item_id } });
                                                } else {
                                                    // Check for existing DSR item request and create if not found
                                                    const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { dsr_detail_id: getDsrDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                                    if (!findDsrItem) {
                                                        const createItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id, dsr_amount: option.dsr_amount, reference_id: option.reference_id, dsr_item_status: dsr_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any);
                                                        dsrItemIds.push(createItem.id); // Add created item ID to the array
                                                    }
                                                }
                                            }
                                        } else {
                                            if (option?.dsr_amount) {
                                                const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id, reference_id: option.reference_id, }, raw: true })
                                                if (!findDsrItem) {
                                                    // Create new DSR item if ID is not provided
                                                    const createItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id, dsr_amount: option.dsr_amount, reference_id: option.reference_id, dsr_item_status: dsr_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any);
                                                    dsrItemIds.push(createItem.id); // Add created item ID to the array
                                                } else {
                                                    dsrItemIds.push(findDsrItem.id);
                                                    await DsrItem.setHeaders(req).update({ dsr_amount: option.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findDsrItem.id } });
                                                }
                                            }

                                        }
                                    }
                                }
                            } else {
                                // Handle single payment type
                                if (category.dsr_amount) {
                                    if (category.dsr_item_id) {
                                        // Update existing DSR item if ID is provided
                                        const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { id: category.dsr_item_id }, raw: true });
                                        if (findDsrItem) {
                                            dsrItemIds.push(findDsrItem.id);
                                            await DsrItem.setHeaders(req).update({ dsr_amount: category.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: category.dsr_item_id } });
                                        } else {
                                            // Check for existing DSR item request and create if not found
                                            const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id } });
                                            if (!findDsrItem) {
                                                const createItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id, dsr_amount: category.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any);
                                                dsrItemIds.push(createItem.id); // Add created item ID to the array
                                            }
                                        }
                                    } else {
                                        const findDsrItem = await DsrItem.findOne({ attributes: ['id'], where: { dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id } })
                                        if (!findDsrItem) {
                                            // Create new DSR item if ID is not provided
                                            const createItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: getDsrDetail.id, payment_type_category_id: category.payment_type_category_id, dsr_amount: category.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any);
                                            dsrItemIds.push(createItem.id); // Add created item ID to the array
                                        } else {
                                            dsrItemIds.push(findDsrItem.id);
                                            await DsrItem.setHeaders(req).update({ dsr_amount: category.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findDsrItem.id } })
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Delete DSR items not in the updated list
                await DsrItem.destroy({ where: { dsr_detail_id: getDsrDetail.id, id: { [Op.notIn]: dsrItemIds } } });

                // Update the total amount for the DSR detail
                const updateDsrDetail: any = {
                    dsr_amount_total: JSON.stringify(dsr_amount_total),
                    updated_by: req.user.id
                }

                // Only super admin, admin, director, accountant can update DSR date
                if (findRole?.role_name == ROLE_CONSTANT.SUPER_ADMIN || findRole?.role_name == ROLE_CONSTANT.ADMIN || findRole?.role_name == ROLE_CONSTANT.DIRECTOR || findRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
                    updateDsrDetail.dsr_date = dsr_date
                }
                await DsrDetail.setHeaders(req).update(updateDsrDetail, { where: { id: dsr_detail_id } });
                return res
                    .status(StatusCodes.OK)
                    .json({ status: true, message: res.__("DSR_UPDATED_SUCCESSFULLY") }); // Return success response
            }
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("DSR_DETAIL_NOT_FOUND") }); // Return error if DSR detail is not found
        }
    } catch (error: any) {
        console.log(error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ status: false, message: res.__("SOMETHING_WENT_WRONG") }); // Return error response
    }
};

const getDsrById = async (req: Request, res: Response) => {
    try {
        // Extracting DSR detail ID from request parameters
        const { dsr_detail_id }: any = req.params;

        // Fetch DSR detail including user and branch information
        const findDsrDetail: any = await DsrDetail.findOne({
            include: [
                {
                    model: User,
                    as: "dsr_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "dsr_branch",
                    attributes: [
                        "id",
                        "branch_name"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                }
            ],
            where: { id: dsr_detail_id },
            raw: true,
            nest: true
        });

        // Check if DSR detail was found
        if (findDsrDetail) {
            // Define base query conditions for payment types
            const whereObj: any = {
                organization_id: req.user.organization_id,
                payment_type_usage: { [Op.in]: [payment_type_usage.COLLECTION, payment_type_usage.OTHER] },
                has_weekly_use: false
            };

            whereObj[Op.or] = [
                { payment_type_status: paymentTypeStatus.ACTIVE },
                {
                    id: {
                        [Op.in]: sequelize.literal(`(SELECT pt.id
                            FROM nv_dsr_items AS di 
                            JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
                            JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
                            WHERE di.dsr_detail_id = ${dsr_detail_id}
                            GROUP BY pt.id
                        )`)
                    }
                }
            ];

            // Fetch Payment Type Details
            let getPaymentTypeDetails: any = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency', [sequelize.literal(`( 
                    SELECT payment_type_remark
                        FROM nv_payment_type_remarks AS pyr
                        WHERE pyr.detail_id = ${findDsrDetail.id} AND pyr.payment_type_id = PaymentType.id )
                    `), 'payment_type_remark']],
                include: [
                    {
                        model: PaymentTypeCategory,
                        as: "payment_type_category",
                        attributes: [
                            ['id', 'payment_type_category_id'],
                            'payment_type_category_remarks',
                            'payment_type_category_title',
                            'payment_type_category_status',
                            'payment_type_category_pattern',
                            'payment_type_category_order',
                            [sequelize.literal(`(SELECT id 
                                FROM nv_payment_type_category_branch 
                                WHERE branch_id = ${findDsrDetail.branch_id} 
                              -- AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                AND parent_id is null 
                              -- AND has_default_active = 1
                                AND payment_type_category_id = payment_type_category.id
                            )`), 'payment_type_category_branch_id'],
                            [sequelize.literal(`( 
                                SELECT di.dsr_amount 
                                    FROM nv_dsr_items AS di 
                                    WHERE di.dsr_detail_id = ${findDsrDetail.id} AND di.payment_type_category_id = payment_type_category.id AND di.dsr_item_status = '${dsr_item_status.ACTIVE}' AND  di.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'dsr_amount'],
                            [sequelize.literal(`( 
                                SELECT di.id 
                                    FROM nv_dsr_items AS di 
                                    WHERE di.dsr_detail_id = ${findDsrDetail.id} AND di.payment_type_category_id = payment_type_category.id AND di.dsr_item_status = '${dsr_item_status.ACTIVE}' AND  di.reference_id IS NULL)
                                `), 'dsr_item_id'],
                        ],
                        where: {
                            [Op.or]: [
                                {
                                    payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT payment_type_category_id 
                                                FROM nv_payment_type_category_branch 
                                                WHERE branch_id = ${findDsrDetail.branch_id} 
                                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND parent_id is null 
                                                AND has_default_active = 1)`)
                                        ]
                                    },
                                },
                                {
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT ptc.id
                                                FROM nv_dsr_items AS di 
                                                JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
                                                WHERE di.dsr_detail_id = ${dsr_detail_id}
                                                GROUP BY ptc.id
                                            )`)
                                        ]
                                    }
                                }
                            ]
                        },
                    }
                ],
                where: whereObj,
                order: [
                    ['payment_type_usage', 'ASC'],
                    ['payment_type_order', 'ASC'],
                    [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
                ],
            });

            // If payment types exist, process the categories and fetch their branches
            if (getPaymentTypeDetails.length > 0) {
                // Convert data to JSON format for easier manipulation
                getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

                // Loop through each payment type's category
                for (const paymentType of getPaymentTypeDetails) {
                    if (paymentType?.payment_type_category?.length > 0) {
                        for (const category of paymentType.payment_type_category) {

                            // Fetch category branches for each category
                            const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                                attributes: [
                                    ['id', 'reference_id'],
                                    [sequelize.literal(`
                                        (SELECT nv_payment_type_category_value.field_value 
                                        FROM nv_payment_type_category_value 
                                        INNER JOIN nv_payment_type_category_field 
                                        ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                        WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                        -- AND nv_payment_type_category_field.field_type = 'string' 
                                        AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                        -- AND nv_payment_type_category_field.payment_type_category_id = ${category.payment_type_category_id}
                                        ORDER BY nv_payment_type_category_value.createdAt ASC 
                                        LIMIT 1
                                    )`), 'first_field_value'],
                                    [sequelize.literal(`( 
                                    SELECT di.dsr_amount 
                                        FROM nv_dsr_items AS di 
                                        WHERE di.dsr_detail_id = ${findDsrDetail.id} AND di.payment_type_category_id = ${category.payment_type_category_id} AND di.dsr_item_status = '${dsr_item_status.ACTIVE}' AND di.reference_id = PaymentTypeCategoryBranch.id 
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'dsr_amount'],
                                    [sequelize.literal(`( 
                                        SELECT di.id 
                                        FROM nv_dsr_items AS di 
                                        WHERE di.dsr_detail_id = ${findDsrDetail.id} AND di.payment_type_category_id = ${category.payment_type_category_id} AND di.dsr_item_status = '${dsr_item_status.ACTIVE}' AND di.reference_id = PaymentTypeCategoryBranch.id )
                                        `), 'dsr_item_id']
                                ],
                                where: {
                                    branch_id: findDsrDetail.branch_id,
                                    parent_id: category.payment_type_category_branch_id,
                                    [Op.or]: [
                                        {
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                            has_default_active: 1,
                                        }, {
                                            id: {
                                                [Op.in]: [sequelize.literal(`(SELECT ptcb.id
                                                    FROM nv_dsr_items AS di 
                                                    LEFT JOIN nv_payment_type_category_branch AS ptcb ON ptcb.id = di.reference_id
                                                    WHERE di.dsr_detail_id = ${dsr_detail_id} AND di.payment_type_category_id = ${category.payment_type_category_id}
                                                    GROUP BY ptcb.id
                                                )`)]
                                            }
                                        }
                                    ]
                                },
                                order: [['payment_type_category_branch_order', 'ASC']],
                            });

                            // Assign found branch value to category
                            category.categoryBranchValue = findCategoryBranch;
                        }
                    }
                }
            }

            // Assign the fetched payment type details to the DSR detail object
            findDsrDetail.dsrItems = getPaymentTypeDetails;

            // Return successful response with the DSR detail and fetched data
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: findDsrDetail,
                vat_per_data: global.config.VAT_PER_DATA
            });
        } else {
            // Return error response if DSR detail is not found
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_DATA_NOT_FOUND"),
                data: {}
            });
        }
    } catch (error) {
        // Log and return error response for any exceptions that occur
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getDsrList = async (req: Request, res: Response) => {
    try {
        // Extract query parameters
        const { page, size, search, branch_id, submitted_start_date, submitted_end_date }: any = req.query;
        const { limit, offset } = getPagination(page, size); // Calculate pagination
        const userDetail: any = await User.findOne({ attributes: ['id', 'user_active_role_id', 'web_user_active_role_id', 'branch_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true }); // Fetch user details

        if (!userDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("ERROR_USER_NOT_FOUND"),
                data: {}
            });
        }

        let findRole;
        // Determine user role based on platform type
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: userDetail.web_user_active_role_id ? userDetail.web_user_active_role_id : userDetail.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && userDetail.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: userDetail.user_active_role_id }, raw: true });
        }

        const whereObj = {
            organization_id: req.user.organization_id
        }; // Initialize where condition object

        const dsrObj: any = {
            where: { dsr_detail_status: { [Op.not]: dsr_detail_status.DELETED } }, // Exclude deleted DSRs
            attributes: [
                'id',
                'dsr_date',
                'dsr_detail_status',
                // Calculate total amount of active items
                [
                    sequelize.literal(`
                      IF(
                        (
                          SELECT ROUND(SUM(dsr_amount), 2)
                          FROM nv_dsr_items AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.dsr_detail_id = DsrDetail.id
                          AND items.dsr_item_status = '${dsr_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ) IS NOT NULL,
                        (
                          SELECT ROUND(SUM(dsr_amount), 2)
                          FROM nv_dsr_items AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.dsr_detail_id = DsrDetail.id
                          AND items.dsr_item_status = '${dsr_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ),
                        0
                      )
                    `),
                    'amount'
                ],
                // Check if there are any requests associated with the DSR
                [sequelize.literal(`EXISTS (SELECT 1 FROM nv_dsr_requests WHERE nv_dsr_requests.dsr_detail_id = DsrDetail.id AND dsr_request_status != '${dsr_request_status.DELETED}')`), 'has_request']
            ],
            include: [
                {
                    model: User,
                    as: "dsr_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn("concat", sequelize.col("dsr_user.user_first_name"), " ", sequelize.col("dsr_user.user_last_name")), // Combine first and last name
                            "user_full_name",
                        ],
                        'employment_number'
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "dsr_detail_updated_by",
                    attributes: [
                        'id',
                        [
                            sequelize.fn("concat", sequelize.col("dsr_detail_updated_by.user_first_name"), " ", sequelize.col("dsr_detail_updated_by.user_last_name")), // Combine first and last name
                            "user_full_name",
                        ],
                        'employment_number'
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "dsr_branch",
                    attributes: ["id", "branch_name", "branch_color", "text_color"],
                    where: whereObj // Branch condition
                },
            ],
            order: [['dsr_date', 'DESC']], // Sort by DSR date descending
        };

        // Apply pagination if page and size are provided
        if (page && size) {
            dsrObj.limit = Number(limit);
            dsrObj.offset = Number(offset);
        }

        // Filter by branch ID if provided
        if (branch_id) {
            dsrObj.where.branch_id = branch_id;
        }

        // Implement search functionality
        if (search) {
            dsrObj.where[Op.or] = [
                { '$dsr_user.user_first_name$': { [Op.like]: `%${search}%` } },
                { '$dsr_user.user_last_name$': { [Op.like]: `%${search}%` } },
                { '$dsr_branch.branch_name$': { [Op.like]: `%${search}%` } }
            ];
        }

        // Restrict results to the user's branch if they are a branch or hotel manager
        if ((findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER) || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER) {
            dsrObj.where.branch_id = userDetail?.branch_id;
        }

        // Filter by submitted date if provided
        if (submitted_start_date && submitted_end_date) {
            dsrObj.where.dsr_date = {
                [Op.between]: [submitted_start_date, submitted_end_date]
            };
        }
        // Execute the query and fetch results
        const { count, rows: getDsrList } = await DsrDetail.findAndCountAll(dsrObj);

        // Get pagination info
        const { total_pages } = getPaginatedItems(size, page, count || 0);

        // Respond with the data
        return res.status(StatusCodes.OK).json({
            status: true,
            data: getDsrList,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });
    } catch (error) {
        console.log(error); // Log the error for debugging
        // Handle errors and respond
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const deleteDsrById = async (req: Request, res: Response) => {
    try {
        // Extract the DSR detail ID from request parameters
        const { dsr_detail_id }: any = req.params;

        // Find the DSR detail by ID
        const findDsrDetail = await DsrDetail.findOne({ attributes: ['id', 'dsr_detail_status'], where: { id: dsr_detail_id }, raw: true });

        // If the DSR detail does not exist, return an error response
        if (!findDsrDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_DSR_NOT_FOUND"),
            });
        }

        // Update the DSR detail status to DELETED
        const deleteDsr = await DsrDetail.setHeaders(req).update(
            { dsr_detail_status: dsr_detail_status.DELETED },
            { where: { id: dsr_detail_id } }
        );

        // Check if the DSR detail was successfully updated
        if (deleteDsr.length > 0) {
            // If so, update the associated DSR items to DELETED status
            await DsrItem.setHeaders(req).update(
                { dsr_item_status: dsr_item_status.DELETED },
                { where: { dsr_detail_id: dsr_detail_id } }
            );

            // Return success response
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("DSR_DELETED_SUCCESSFULLY"),
            });
        } else {
            // If the update failed, return a failure response
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAILED_TO_DELETE_DSR"),
            });
        }
    } catch (error) {
        console.log(error); // Log the error for debugging
        // Handle errors and respond
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getDsrRequestList = async (req: Request, res: Response) => {
    try {
        // Destructure query parameters from the request
        const { page, size, search, branch_id, dsr_id, submitted_start_date, submitted_end_date, request_status }: any = req.query;

        // Get pagination values (limit and offset) based on page and size
        const { limit, offset } = getPagination(page, size);
        const branchWhereObj: any = {
            organization_id: req.user.organization_id
        }; // Object to hold branch filter criteria

        // Fetch user details based on the logged-in user ID
        const userDetail: any = await User.findOne({ attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true });

        let findRole; // Variable to store user role

        if (!userDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("ERROR_USER_NOT_FOUND"),
                data: {}
            });
        }
        // Determine user role based on platform type (web, iOS, or Android)
        if (req.headers["platform-type"] === "web") {
            findRole = await Role.findOne({ where: { id: userDetail.web_user_active_role_id ? userDetail.web_user_active_role_id : userDetail.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] === "ios" || req.headers["platform-type"] === "android") && userDetail.user_active_role_id) {
            findRole = await Role.findOne({ where: { id: userDetail.user_active_role_id }, raw: true });
        }
        // Set branch filter criteria if branch_id is provided
        if (branch_id) {
            branchWhereObj.id = branch_id;
        }

        // If the user is a Branch Manager or Hotel Manager, filter by their branch ID
        if ((findRole?.role_name === ROLE_CONSTANT.BRANCH_MANAGER) || findRole?.role_name === ROLE_CONSTANT.HOTEL_MANAGER) {
            branchWhereObj.id = userDetail?.branch_id;
        }

        const dsrWhereObj: any = {}; // Object to hold DSR filter criteria

        // Set DSR filter criteria based on submitted_start_date and submitted_end_date and dsr_id if provided
        if (submitted_start_date && submitted_end_date) {
            dsrWhereObj.dsr_date = {
                [Op.between]: [submitted_start_date, submitted_end_date]
            };
        }
        if (dsr_id) {
            dsrWhereObj.id = dsr_id;
        }

        // Define the main query object for DSR requests
        const dsrObj: any = {
            where: { dsr_request_status: { [Op.not]: dsr_request_status.DELETED } }, // Exclude deleted requests
            attributes: [
                'id',
                'dsr_detail_id',
                'dsr_detail.branch_id',
                'dsr_detail.dsr_date',
                'dsr_detail.dsr_detail_status',
                'dsr_request_status',
                [
                    sequelize.literal(`
                      IF(
                        (
                        SELECT ROUND(SUM(dsr_amount), 2)
                          FROM nv_dsr_item_requests AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.dsr_request_detail_id = DsrRequest.id
                          AND items.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ) IS NOT NULL,
                        (
                          SELECT ROUND(SUM(dsr_amount), 2)
                          FROM nv_dsr_item_requests AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.dsr_request_detail_id = DsrRequest.id
                          AND items.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ),
                        0
                      )
                    `),
                    'amount'
                ]
            ],
            include: [
                {
                    model: DsrDetail,
                    as: "dsr_detail",
                    attributes: [
                        "id",
                        "branch_id",
                        "dsr_date",
                        "dsr_detail_status"
                    ],
                    where: dsrWhereObj,
                    include: [{
                        model: Branch,
                        as: "dsr_branch",
                        attributes: [
                            "id",
                            "branch_name",
                            "branch_color", "text_color"
                        ],
                        where: branchWhereObj // Filter by branch criteria
                    }]
                },
                {
                    model: User,
                    as: "dsr_request_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("dsr_request_user.user_first_name"),
                                " ",
                                sequelize.col("dsr_request_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "dsr_request_updated_by",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("dsr_request_updated_by.user_first_name"),
                                " ",
                                sequelize.col("dsr_request_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                }
            ],
            order: [['updatedAt', 'DESC']], // Sort by updatedAt in descending order
            raw: true,
            nest: true // Use nested structure in results
        };
        // Add pagination to the query if page and size are provided
        if (page && size) {
            dsrObj.limit = Number(limit);
            dsrObj.offset = Number(offset);
        }

        // Add search criteria if provided
        if (search) {
            dsrObj.where[Op.or] = [
                { '$dsr_request_user.user_first_name$': { [Op.like]: `%${search}%` } },
                { '$dsr_request_user.user_last_name$': { [Op.like]: `%${search}%` } },
                { '$dsr_detail.dsr_branch.branch_name$': { [Op.like]: `%${search}%` } }
            ];
        }

        // Filter by request status if provided
        if (request_status) {
            dsrObj.where.dsr_request_status = request_status;
        }

        // Execute the query to fetch DSR requests
        const { count, rows: getDsrList } = await DsrRequest.findAndCountAll(dsrObj);

        // Transform the fetched data for response
        const transformedData = getDsrList.map((item: any) => {
            return {
                id: item.id,
                dsr_date: item.dsr_detail.dsr_date,
                dsr_detail_status: item.dsr_detail.dsr_detail_status,
                dsr_request_status: item.dsr_request_status,
                amount: item.amount,
                dsr_user: {
                    id: item.dsr_request_user.id,
                    user_full_name: item.dsr_request_user.user_full_name
                },
                dsr_branch: {
                    id: item.dsr_detail.dsr_branch?.id,
                    branch_name: item.dsr_detail.dsr_branch?.branch_name,
                    branch_color: item.dsr_detail.dsr_branch?.branch_color,
                    text_color: item.dsr_detail.dsr_branch?.text_color
                },
                dsr_request_updated_by: {
                    id: item.dsr_request_updated_by.id,
                    user_full_name: item.dsr_request_updated_by.user_full_name
                },
            };
        });

        // Get pagination info for the response
        const { total_pages } = getPaginatedItems(size, page, count || 0);

        // Return successful response with data and pagination info
        return res.status(StatusCodes.OK).json({
            status: true,
            data: transformedData,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });
    } catch (error) {
        console.log(error); // Log any errors for debugging
        // Handle errors and return response
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getDsrRequestById = async (req: Request, res: Response) => {
    try {
        // Extracting dsr_request_id from request parameters
        const { dsr_request_id }: any = req.params;

        // Finding the DSR request by ID, including user and detail associations
        const findDsrRequest: any = await DsrRequest.findOne({
            include: [
                {
                    model: User,
                    as: "dsr_request_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("dsr_request_user.user_first_name"),
                                " ",
                                sequelize.col("dsr_request_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "dsr_request_updated_by",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("dsr_request_updated_by.user_first_name"),
                                " ",
                                sequelize.col("dsr_request_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: DsrDetail,
                    as: "dsr_detail",
                    attributes: [
                        "id",
                        "branch_id", "dsr_date", "dsr_detail_status"
                    ],
                    include: [{
                        model: Branch,
                        as: "dsr_branch",
                        attributes: [
                            "id",
                            "branch_name"
                        ],
                        where: {
                            organization_id: req.user.organization_id
                        }
                    }]
                }
            ],
            where: { id: dsr_request_id }, // Condition to find specific DSR request
            raw: true, // Return raw data instead of Sequelize instances
            nest: true // Nest the results for easier access
        });

        if (findDsrRequest) {
            // Define query conditions for fetching payment types
            const whereObj: any = {
                organization_id: req.user.organization_id,
                payment_type_usage: { [Op.in]: [payment_type_usage.COLLECTION, payment_type_usage.OTHER] },
                has_weekly_use: false
            };

            // Add OR condition for payment type status
            whereObj[Op.or] = [
                { payment_type_status: paymentTypeStatus.ACTIVE },
                {
                    id: {
                        [Op.in]: sequelize.literal(`(SELECT pt.id
                            FROM nv_dsr_item_requests AS dir 
                            JOIN nv_payment_type_category AS ptc ON ptc.id = dir.payment_type_category_id
                            JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
                            WHERE dir.dsr_request_detail_id = ${findDsrRequest.id}
                            GROUP BY pt.id
                        )`)
                    }
                }
            ];

            // Fetching Payment Type Details based on conditions
            let getPaymentTypeDetails: any = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency', [sequelize.literal(`( 
                    SELECT payment_type_remark
                        FROM nv_payment_type_remarks AS pyr
                        WHERE pyr.detail_id = ${findDsrRequest?.dsr_detail?.id} AND pyr.payment_type_id = PaymentType.id )
                    `), 'payment_type_remark']],
                include: [
                    {
                        model: PaymentTypeCategory,
                        as: "payment_type_category",
                        attributes: [
                            ['id', 'payment_type_category_id'],
                            'payment_type_category_remarks',
                            'payment_type_category_title',
                            'payment_type_category_status',
                            'payment_type_category_pattern',
                            'payment_type_category_order',
                            [sequelize.literal(`(SELECT id 
                            FROM nv_payment_type_category_branch 
                            WHERE branch_id = ${findDsrRequest?.dsr_detail?.branch_id} 
                            -- AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                            AND parent_id is null 
                            AND has_default_active = 1
                            AND payment_type_category_id = payment_type_category.id
                        )`), 'payment_type_category_branch_id'],
                            [sequelize.literal(`( 
                                    SELECT dir.dsr_amount 
                                    FROM nv_dsr_item_requests AS dir 
                                    WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}' AND dir.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'dsr_amount'],
                            [sequelize.literal(`( 
                                    SELECT dir.id 
                                    FROM nv_dsr_item_requests AS dir 
                                    WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}' AND dir.reference_id IS NULL)
                                `), 'dsr_request_item_id'],
                            [sequelize.literal(`( 
                                    SELECT dir.old_dsr_amount 
                                    FROM nv_dsr_item_requests AS dir 
                                    WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}' AND dir.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'old_dsr_amount']
                        ],
                        where: {
                            [Op.or]: [
                                {
                                    payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT payment_type_category_id 
                                                FROM nv_payment_type_category_branch 
                                                WHERE branch_id = ${findDsrRequest?.dsr_detail?.branch_id} 
                                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND parent_id is null 
                                                AND has_default_active = 1)`)
                                        ]
                                    },
                                },
                                {
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT ptc.id
                                                FROM nv_dsr_item_requests AS dir 
                                                JOIN nv_payment_type_category AS ptc ON ptc.id = dir.payment_type_category_id
                                                WHERE dir.dsr_request_detail_id = ${findDsrRequest.id}
                                                GROUP BY ptc.id
                                            )`)
                                        ]
                                    }
                                }
                            ]
                        },
                    }
                ],
                where: whereObj, // Applying defined conditions
                order: [
                    ['payment_type_usage', 'ASC'],
                    ['payment_type_order', 'ASC'],
                    [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
                ],
            });

            // If payment types exist, process categories and fetch their branches
            if (getPaymentTypeDetails.length > 0) {
                // Convert data to JSON format for easier manipulation
                getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

                // Loop through each payment type's category
                for (const paymentType of getPaymentTypeDetails) {
                    if (paymentType?.payment_type_category?.length > 0) {
                        for (const category of paymentType.payment_type_category) {
                            // Fetch category branches for each category
                            const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                                attributes: [
                                    ['id', 'reference_id'],
                                    [sequelize.literal(`(SELECT nv_payment_type_category_value.field_value 
                                        FROM nv_payment_type_category_value 
                                        INNER JOIN nv_payment_type_category_field 
                                        ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                        WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                        -- AND nv_payment_type_category_field.field_type = 'string' 
                                        AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                        ORDER BY nv_payment_type_category_value.createdAt ASC 
                                        LIMIT 1
                                    )`), 'first_field_value'],
                                    [sequelize.literal(`( 
                                    SELECT dir.dsr_amount 
                                        FROM nv_dsr_item_requests AS dir 
                                        WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id 
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'dsr_amount'],
                                    [sequelize.literal(`( 
                                        SELECT dir.id 
                                        FROM nv_dsr_item_requests AS dir
                                        WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id )
                                        `), 'dsr_item_id'],
                                    [sequelize.literal(`( 
                                            SELECT dir.old_dsr_amount 
                                                FROM nv_dsr_item_requests AS dir 
                                                WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.dsr_request_item_status = '${dsr_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id 
                                            UNION SELECT 0 LIMIT 1)
                                            `), 'old_dsr_amount']
                                ],
                                where: {
                                    branch_id: findDsrRequest?.dsr_detail?.branch_id,
                                    parent_id: category.payment_type_category_branch_id,
                                    [Op.or]: [
                                        {
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                            has_default_active: 1
                                        }, {
                                            id: {
                                                [Op.in]: [sequelize.literal(`(SELECT ptcb.id
                                                    FROM nv_dsr_item_requests AS dir 
                                                    LEFT JOIN nv_payment_type_category_branch AS ptcb ON ptcb.id = dir.reference_id
                                                    WHERE dir.dsr_request_detail_id = ${findDsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id}
                                                    GROUP BY ptcb.id
                                                )`)]
                                            }
                                        }
                                    ]
                                },
                                order: [['payment_type_category_branch_order', 'ASC']],
                            });

                            // Assign found branch value to category
                            category.categoryBranchValue = findCategoryBranch;
                        }
                    }
                }
            }

            // Sending response with DSR request details
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: {
                    id: findDsrRequest?.dsr_detail?.id,
                    dsr_date: findDsrRequest?.dsr_detail?.dsr_date,
                    dsr_detail_status: findDsrRequest?.dsr_detail?.dsr_detail_status,
                    request_remark: findDsrRequest.request_remark,
                    dsr_request_status: findDsrRequest.dsr_request_status,
                    dsr_amount_total: findDsrRequest.dsr_amount_total,
                    dsr_branch: findDsrRequest?.dsr_detail?.dsr_branch?.branch_name,
                    dsr_branch_id: findDsrRequest?.dsr_detail?.dsr_branch?.id,
                    submitted_user: findDsrRequest?.dsr_request_user?.user_full_name,
                    dsr_request_updated_by: findDsrRequest?.dsr_request_updated_by,
                    dsr_items: getPaymentTypeDetails || [], // Include payment type details
                    old_dsr_amount_total: findDsrRequest?.old_dsr_amount_total
                },
                vat_per_data: global.config.VAT_PER_DATA // Include VAT percentage data
            });
        } else {
            // If no request found, send a not found response
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("REQUEST_NOT_FOUND")
            });
        }
    } catch (error: any) {
        // Log error and send a server error response
        console.log(error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG")
        });
    }
};


const approveRejectRequest = async (req: Request, res: Response) => {
    try {
        // Destructuring request body to get necessary fields
        const { dsr_request_id, request_status, request_remark }: any = req.body;

        // Finding the DSR request based on ID and ensuring it is in a PENDING state
        const findDsrDetail = await DsrRequest.findOne({ attributes: ['id', 'dsr_detail_id', 'dsr_amount_total', 'created_by'], where: { id: dsr_request_id, dsr_request_status: dsr_request_status.PENDING }, raw: true });

        // If the DSR request is not found, respond with an error
        if (!findDsrDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_DSR_REQUEST_NOT_FOUND"),
            });
        }

        // Finding the associated DSR date
        const findDsrDate: any = await DsrDetail.findOne({ attributes: ['id', 'user_id', 'branch_id'], where: { id: findDsrDetail.dsr_detail_id }, raw: true });

        // Finding the user associated with the DSR date
        const findUser = await User.findOne({ attributes: ['id', 'webAppToken', 'appToken'], where: { id: findDsrDate?.user_id, organization_id: req.user.organization_id }, raw: true });
        if (!findUser) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ERROR_USER_NOT_FOUND"),
            });
        }
        const getActionUserFullName: any = await getUserFullName(req.user.id);

        // If the request is approved
        if (request_status == dsr_request_status.APPROVED) {
            // Updating the DSR request status to APPROVED
            const updateDsrRequest = await DsrRequest.setHeaders(req).update({ dsr_request_status: dsr_request_status.APPROVED, request_remark: request_remark, updated_by: req.user.id }, { where: { id: dsr_request_id } });

            // If the update was successful
            if (updateDsrRequest.length > 0) {
                // Updating the total amount in the DSR detail
                await DsrDetail.update({ dsr_amount_total: findDsrDetail.dsr_amount_total, updated_by: req.user.id }, { where: { id: findDsrDate.id } });

                // Finding active DSR items associated with the request
                const findDsrItemList = await DsrItemRequest.findAll({ attributes: ['id', 'payment_type_category_id', 'reference_id', 'dsr_amount'], where: { dsr_request_detail_id: findDsrDetail.id, dsr_request_item_status: dsr_request_item_status.ACTIVE }, raw: true });

                // If there are items in the DSR item list
                if (findDsrItemList.length > 0) {
                    const dsrItemIds = [];

                    // Processing each item in the DSR item list
                    for (const dsrItem of findDsrItemList) {
                        const findDsrItem = await DsrItem.findOne({ attributes: ['id', 'reference_id'], where: { dsr_detail_id: findDsrDetail.dsr_detail_id, payment_type_category_id: dsrItem.payment_type_category_id, dsr_item_status: dsr_item_status.ACTIVE, reference_id: dsrItem.reference_id }, raw: true });

                        if (findDsrItem) {
                            // If the reference ID exists and matches
                            if (dsrItem.reference_id) {
                                if (dsrItem.reference_id == findDsrItem.reference_id) {
                                    // Update existing DSR item if the reference ID matches
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: dsrItem.reference_id, branch_id: findDsrDate.branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    });

                                    if (findDocumentCategoryBranch) {
                                        await DsrItem.setHeaders(req).update({ dsr_amount: dsrItem.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, updated_by: findDsrDetail.created_by }, { where: { dsr_detail_id: findDsrDetail.dsr_detail_id, payment_type_category_id: dsrItem.payment_type_category_id, id: findDsrItem.id } });
                                        dsrItemIds.push(findDsrItem.id);
                                    }
                                } else {
                                    // If the reference ID does not match, create a new DSR item
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: dsrItem.reference_id, branch_id: findDsrDate.branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    });

                                    if (findDocumentCategoryBranch) {
                                        const createDsrItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: findDsrDetail.dsr_detail_id, payment_type_category_id: dsrItem?.payment_type_category_id, dsr_amount: dsrItem.dsr_amount, reference_id: dsrItem.reference_id, dsr_item_status: dsr_item_status.ACTIVE, created_by: findDsrDetail.created_by, updated_by: findDsrDetail.created_by } as any);
                                        if (createDsrItem) {
                                            dsrItemIds.push(createDsrItem.id);
                                        }
                                    }
                                }
                            } else {
                                // Update the DSR item if no reference ID is provided
                                await DsrItem.setHeaders(req).update({ dsr_amount: dsrItem.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, updated_by: findDsrDetail.created_by }, { where: { dsr_detail_id: findDsrDetail.dsr_detail_id, payment_type_category_id: dsrItem.payment_type_category_id, id: findDsrItem.id } });
                                dsrItemIds.push(findDsrItem.id);
                            }
                        } else {
                            // If no DSR item exists, create one
                            if (dsrItem.reference_id) {
                                const createDsrItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: findDsrDetail.dsr_detail_id, payment_type_category_id: dsrItem?.payment_type_category_id, dsr_amount: dsrItem.dsr_amount, reference_id: dsrItem.reference_id, dsr_item_status: dsr_item_status.ACTIVE, created_by: findDsrDetail.created_by, updated_by: findDsrDetail.created_by } as any);
                                if (createDsrItem) {
                                    dsrItemIds.push(createDsrItem.id);
                                }
                            } else {
                                const createDsrItem = await DsrItem.setHeaders(req).create({ dsr_detail_id: findDsrDetail.dsr_detail_id, payment_type_category_id: dsrItem?.payment_type_category_id, dsr_amount: dsrItem.dsr_amount, dsr_item_status: dsr_item_status.ACTIVE, created_by: findDsrDetail.created_by, updated_by: findDsrDetail.created_by } as any);
                                if (createDsrItem) {
                                    dsrItemIds.push(createDsrItem.id);
                                }
                            }
                        }
                    }

                    // If there are items that are not processed, mark them as inactive
                    if (dsrItemIds.length > 0) {
                        await DsrItem.setHeaders(req).update({ dsr_item_status: dsr_item_status.INACTIVE }, { where: { id: { [Op.notIn]: dsrItemIds }, dsr_detail_id: findDsrDetail.dsr_detail_id } });
                    }
                }

                // Creating a notification for the user regarding the DSR approval
                await createNotification(
                    [findUser],
                    req,
                    NOTIFICATION_TYPE.INDIVIDUAL,
                    NOTIFICATIONCONSTANT.DSR_RESPONSE.content(getActionUserFullName, dsr_request_status.APPROVED, moment(findDsrDate?.dsr_date).format("DD/MM/YYYY")),
                    NOTIFICATIONCONSTANT.DSR_RESPONSE.heading,
                    REDIRECTION_TYPE.DSR_REQUEST,
                    findDsrDate.id,
                    { dsr_id: findDsrDate.id, request_id: findDsrDetail.id }
                );
            }

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("DSR_REQUEST_APPROVED"),
            });
        }
        // If the request is rejected
        else if (request_status == dsr_request_status.REJECTED) {
            // Updating the DSR request status to REJECTED
            const updateDsrRequest = await DsrRequest.setHeaders(req).update({ dsr_request_status: dsr_request_status.REJECTED, request_remark: request_remark, updated_by: req.user.id }, { where: { id: dsr_request_id } });

            // If the update was successful
            if (updateDsrRequest.length > 0) {
                // Setting the DSR item request status back to ACTIVE
                await DsrItemRequest.setHeaders(req).update({ dsr_request_item_status: dsr_request_item_status.ACTIVE }, { where: { dsr_request_detail_id: dsr_request_id } });

                // Creating a notification for the user regarding the DSR rejection
                await createNotification(
                    [findUser],
                    req,
                    NOTIFICATION_TYPE.INDIVIDUAL,
                    NOTIFICATIONCONSTANT.DSR_RESPONSE.content(getActionUserFullName, dsr_request_status.REJECTED, moment(findDsrDate?.dsr_date).format("DD/MM/YYYY")),
                    NOTIFICATIONCONSTANT.DSR_RESPONSE.heading,
                    REDIRECTION_TYPE.DSR_REQUEST,
                    findDsrDate.id,
                    { dsr_id: findDsrDate.id, request_id: findDsrDetail.id }
                );

                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("DSR_REQUEST_REJECTED"),
                });
            }
        }
    } catch (error) {
        // Handling any unexpected errors
        console.error("Error in approveRejectRequest:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG")
        });
    }
};

const getDsrReport = async (req: Request, res: Response) => {
    try {
        // Destructuring query parameters from the request
        const { start_date, end_date, dsr_payment_type_category, branch_id, date_filter, time_period, columns_group, report_type }: any = req.body;

        // Generating the report data based on the provided parameters
        const reportData = await generateReportNew(branch_id, start_date, end_date, dsr_payment_type_category, date_filter, time_period, columns_group, report_type, req.user.organization_id);

        // Sending a successful response with the report data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: reportData,
        });
    } catch (error) {
        // Logging the error and sending an error response
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const checkDsrExist = async (req: Request, res: Response) => {
    try {
        // Destructuring request body to get branch_id and dsr_date
        const { branch_id, dsr_date }: any = req.body;

        // Searching for an existing DSR detail that matches the branch_id and dsr_date
        const getDsrDetail = await DsrDetail.findOne({
            attributes: ['id'],
            where: {
                branch_id: branch_id,
                dsr_date: dsr_date,
                dsr_detail_status: dsr_detail_status.ACTIVE
            }, raw: true
        });

        // If a DSR detail is found, respond with an error indicating it's already added
        if (getDsrDetail) {
            return res.status(StatusCodes.ACCEPTED).json({
                status: false,
                message: res.__("DSR_ALREADY_ADDED")
            });
        } else {
            // If no DSR detail is found, respond indicating it doesn't exist
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FAIL_DSR_NOT_FOUND")
            });
        }
    } catch (error) {
        // Logging the error for debugging purposes
        console.log(error);
        // Responding with an error indicating something went wrong
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error // Including the error data in the response for further investigation
        });
    }
};

const getDsrActivity = async (req: Request, res: Response) => {
    try {
        // Extracting dsr_id from request parameters and pagination details from query
        const { dsr_id }: any = req.params;
        const { page, size }: any = req.query;

        // Getting pagination limit and offset
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Finding DSR detail with associated User and Branch models
        const findDsrDetail: any = await DsrDetail.findOne({
            include: [
                {
                    model: User,
                    as: "dsr_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"

                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "dsr_branch",
                    attributes: [
                        "id",
                        "branch_name"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
            ],
            where: {
                id: dsr_id,
                dsr_detail_status: { [Op.not]: dsr_detail_status.DELETED }
            },
            logging: console.log,
            raw: true,
            nest: true
        });

        // Handling case where DSR detail is not found
        if (!findDsrDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_DSR_NOT_FOUND"),
            });
        }

        // Building the query to find associated activities
        const dsrDetailObj: any = {
            include: [
                {
                    model: User,
                    as: "users",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "user_email",
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
            ],
            where: {
                [Op.or]: [
                    {
                        activity_table: { [Op.in]: ['DsrItem', 'DsrRequest'] },
                        [Op.or]: [
                            { previous_data: { [Op.like]: `%"dsr_detail_id":${dsr_id}%` } },
                            { new_data: { [Op.like]: `%"dsr_detail_id":${dsr_id}%` } }
                        ]
                    },
                    {
                        activity_table: { [Op.in]: ['DsrDetail'] },
                        [Op.or]: [
                            { previous_data: { [Op.like]: `%"id":${dsr_id}%` } },
                            { new_data: { [Op.like]: `%"id":${dsr_id}%` } }
                        ]
                    }
                ]
            },
            logging: console.log,
            order: [['createdAt', 'DESC']]
        };

        // Adding pagination details if page and size are provided
        if (page && size) {
            dsrDetailObj.limit = Number(limit);
            dsrDetailObj.offset = Number(offset);
        }

        // Fetching activities with count
        const { count, rows: getDsrList } = await Activity.findAndCountAll(dsrDetailObj);
        const getDsrDetail = JSON.parse(JSON.stringify(getDsrList));

        // Checking if activities were found
        if (getDsrDetail.length > 0) {
            // Formatting the activity_table names
            getDsrDetail.forEach((log: any) => {
                log.activity_table = addSpacesBeforeCapitals(log.activity_table);
            });

            // Calculating total pages for pagination
            const { total_pages } = getPaginatedItems(size, page, count || 0);

            // Responding with the fetched data
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: {
                    id: findDsrDetail?.id,
                    dsr_branch: findDsrDetail?.dsr_branch?.branch_name,
                    dsr_user: findDsrDetail?.dsr_user?.user_full_name,
                    dsr_date: findDsrDetail.dsr_date,
                    dsr_activity: getDsrDetail
                },
                page: parseInt(page),
                size: parseInt(size),
                count: count,
                total_pages,
            });
        } else {
            // Responding with no data found
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FAIL_DATA_FETCHED"),
                data: [],
                page: 0,
                size: 0,
                count: 0,
                total_pages: 0,
            });
        }
    } catch (error) {
        // Logging the error and responding with a service unavailable status
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const downloadPdfExcel = async (req: Request, res: Response) => {
    try {
        const { file_type, start_date, end_date, dsr_payment_type_category, branch_id, date_filter, time_period, columns_group, report_type }: any = req.body;
        const dataset: any = await generateReportNew(branch_id, start_date, end_date, dsr_payment_type_category, date_filter, time_period, columns_group, report_type, req.user.organization_id);

        const generalSettings = await getGeneralSettingObj(req.user.organization_id)
        const currencySign = generalSettings?.currency ? JSON.parse(generalSettings?.currency)?.symbol : ''
        const columnsGroup = dataset.columns_group;

        const addCurrencySign = (data: any, currencySign = "$") => {
            data.forEach((column: any) => {
                if (column.has_field_currency === 1) {
                    column.content = `${column.content} (${currencySign}) `;
                }
                if (column.type == 'total') {
                    column.content = `${column.content} (${column.SelectedData.join(",")}) `
                }
                // If the column has children, process them recursively
                if (Array.isArray(column.children)) {
                    addCurrencySign(column.children, currencySign);
                }
            });
        };

        addCurrencySign(columnsGroup, currencySign)

        // Add a currency sign to the relevant content
        dataset.columns_group = columnsGroup;

        let branchName: any
        if (branch_id) {
            const branchId = branch_id
                ? branch_id.split(",").map(Number)
                : [];
            const findBranchName = await Branch.findAll({ attributes: ['id', 'branch_name'], where: { id: { [Op.in]: branchId }, organization_id: req.user.organization_id, branch_status: { [Op.not]: branch_status.DELETED } }, raw: true })
            if (findBranchName) {
                branchName = findBranchName.map((type) => type.branch_name).join(',');
            }
        } else {
            branchName = 'ALL'
        }
        let timePeriod: any
        if (time_period == 'daily') {
            timePeriod = "Day";
        } else if (time_period == 'monthly') {
            timePeriod = "Month";
        } else if (time_period == 'yearly') {
            timePeriod = "Year";
        } else if (time_period == 'weekly') {
            timePeriod = "Week";
        } else if (time_period == 'quarterly') {
            timePeriod = "Quarter";
        } else {
            timePeriod = ''
        }
        if (dataset) {
            if (file_type == 'excel') {
                const workbook: any = await generateFile(file_type)
                const worksheet: any = workbook.addWorksheet('Logs Book Report');

                // Add title and metadata
                const titleRow = worksheet.getCell('A1');
                titleRow.value = 'Logs Book Report';
                titleRow.font = { bold: true, size: 14 };
                titleRow.alignment = { horizontal: 'center' };

                const titleLength = titleRow.value.length;
                worksheet.getColumn('A').width = titleLength + 5;
                worksheet.mergeCells(`A1:${String.fromCharCode(65 + dataset.column.length - 1)}1`)
                if (report_type == 'day') {
                    worksheet.addRow(['Date:', date_filter == 'custom' ? `${moment(start_date).format("DD-MM-YYYY")} To ${moment(end_date).format("DD-MM-YYYY")} ` : getColumnValue(date_filter, start_date)]);
                } else {
                    worksheet.addRow(['Date:', getColumnValueForGeneral(date_filter, start_date, end_date)]);
                }

                worksheet.addRow(['Branch:', branchName]);

                if (timePeriod) {
                    worksheet.addRow(['Time period :', timePeriod]);
                } else {
                    worksheet.addRow(['']);
                }
                worksheet.addRow(['Report Date:', moment().format('DD MMMM YYYY, h:mm A')]);
                worksheet.addRow([]); // Empty row for spacing

                // Create grouped header and subheader
                const headers: any = [];
                const columnsGroup = dataset.columns_group;

                // Prepare grouped and subheaders
                const groupedHeaderRow: any = [];
                const subHeaderRow: any = [];

                let groupIndex: any = 1;
                columnsGroup.forEach((group: any) => {

                    if (group.type === 'Group') {
                        group.children.forEach((child: any) => {
                            groupedHeaderRow.push({ value: group.content, span: group.children.length, groupIndex: groupIndex });
                            headers.push(child.key)
                        });

                        // groupedHeaderRow.push({ value: group.content, span: group.children.length });
                        subHeaderRow.push(...group.children.map((child: any) => child.content));
                    } else {
                        groupedHeaderRow.push({ value: group.content, span: 1, groupIndex: groupIndex });
                        subHeaderRow.push("");
                        headers.push(group.key)
                    }
                    groupIndex++;
                });

                const mergedHeaders: any[] = []; // Array to store merged rows
                const currentColStartIndex = 1; // Initial column index

                // Add grouped headers
                const groupedRow = worksheet.addRow(
                    groupedHeaderRow.map((header: any) => {
                        mergedHeaders.push(header.value); // Add the value to the mergedHeaders array
                        return header.value;
                    })
                );

                let currentCol: any = currentColStartIndex; // Start from the initial column index
                let groupIndexCheck = 0
                groupedRow.eachCell((cell: any, colNumber: number) => {
                    const header = groupedHeaderRow[colNumber - 1];
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.font = { bold: true };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Blue background
                    cell.font.color = { argb: 'FFFFFF' };

                    if (groupIndexCheck != header.groupIndex && header.span > 1) {
                        const startCol = getColumnLetter(currentCol);
                        const endCol = getColumnLetter(currentCol + header.span - 1);
                        worksheet.mergeCells(`${startCol}7:${endCol}7`);
                    }
                    currentCol++;
                    groupIndexCheck = header.groupIndex

                });

                // Add subheaders
                const subHeaderRowExcel = worksheet.addRow(subHeaderRow);

                subHeaderRowExcel.eachCell((cell: any, colNumber: number) => {
                    // Current row number
                    const currentRowNumber = cell.row;

                    // Get the cell above
                    const upperCell = worksheet.getRow(currentRowNumber - 1).getCell(colNumber);

                    // Merge the current cell with the upper cell
                    const startCell = `${cell.address}`; // Current cell address (e.g., B5)
                    const endCell = `${upperCell.address}`; // Upper cell address (e.g., B4)
                    if (!cell.value)
                        worksheet.mergeCells(`${endCell}:${startCell}`);

                    // Example: Logging the value of the cell above
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.font = { bold: true };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '0070C0' } }; // Dark gray background
                    cell.font.color = { argb: 'FFFFFF' }; // White font
                });

                // Adjust column widths
                subHeaderRowExcel.eachCell({ includeEmpty: true }, (cell: any, colNumber: any) => {
                    const column = worksheet.getColumn(colNumber);
                    column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);
                });
                const parseValue = (value: any) => {
                    return isNaN(value) ? value : parseFloat(value); // Convert strings to numbers where applicable
                };

                // Add data rows
                dataset.data.forEach((item: any) => {
                    const row = headers.map((_: any,) => {
                        const key = _;
                        return parseValue(item[key] !== undefined ? item[key] : '');
                    });
                    const dataRow = worksheet.addRow(row);
                    dataRow.eachCell({ includeEmpty: true }, (cell: any, colNumber: any) => {
                        const column = worksheet.getColumn(colNumber);
                        column.width = Math.max((cell.value || '').toString().length + 2, column.width || 10);
                    });
                });

                // Add total row
                const totalRowValues = headers.map((_: any) => {
                    const key = _;
                    return parseValue(dataset.total[key] || '');
                });
                const totalRow = worksheet.addRow(totalRowValues);
                totalRow.font = { bold: true };
                totalRow.eachCell((cell: any) => {
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'd9ead3' } }; // Light green background
                });

                // Send the workbook as a buffer
                const buffer = await workbook.xlsx.writeBuffer();
                res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
                res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                res.send(buffer);
            } else if (file_type == 'pdf') {
                const findUserDetail: any = await User.findOne({
                    where: { id: req.user.id, organization_id: req.user.organization_id }, attributes: ['id', [
                        sequelize.fn(
                            "concat",
                            sequelize.col("user_first_name"),
                            " ",
                            sequelize.col("user_last_name"),
                        ),
                        "user_full_name",
                    ],], raw: true
                })

                const [contentHtml, footerHtml] = await Promise.all([
                    new Promise((resolve, reject) => {
                        readHTMLFile(
                            path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.DSR_PDF.template}.html`),
                            (err: any, html: any) => {
                                if (err) reject(err);
                                else resolve(html);
                            }
                        );
                    }),
                    new Promise((resolve, reject) => {
                        readHTMLFile(
                            path.join(__dirname, `../../src/email_templates/${DSRCONSTANT.FOOTER_TEMPLATE.template}.html`),
                            (err: any, html: any) => {
                                if (err) reject(err);
                                else resolve(html);
                            }
                        );
                    }),
                ]);

                const combinedTemplate = `${contentHtml}${footerHtml}`;
                const compiledTemplate = handlebars.compile(combinedTemplate);
                const dateFiler = report_type == 'day' ? date_filter == 'custom' ? `${moment(start_date).format("DD-MM-YYYY")} To ${moment(end_date).format("DD-MM-YYYY")}` : getColumnValue(date_filter, start_date) : getColumnValueForGeneral(date_filter, start_date, end_date)

                const splitedData = splitData({
                    columns_group: dataset.columns_group, data: [...dataset.data, dataset.total]
                }, 5)

                const dataObj = {
                    branch: branchName,
                    date: dateFiler,
                    splitedData: splitedData,
                    total: dataset.total,
                    time_period: timePeriod,
                    current_date: moment().format('DD MMMM YYYY, h:mm A'),
                    NAMASTE_LOGO: await getOrganizationLogo(req.user.organization_id),
                    GENERATED_BY_USER: findUserDetail.user_full_name,
                    CONFIDENTIALITY_STATEMENT: EMAIL_ADDRESS.CONFIDENTIALITY_STATEMENT,
                    MICROFFICE_LOGO_URL: global.config.API_UPLOAD_URL + "/email_logo/logo.png"
                };

                const htmlToSend = compiledTemplate(dataObj);

                const pdfBuffer: any = await generateFile(file_type, htmlToSend)

                res.set({
                    'Content-Type': 'application/pdf',
                    'Content-Disposition': 'attachment; filename=generated.pdf',
                    'Content-Length': pdfBuffer.length
                });

                console.log("PDF generated and sent successfully!");
                res.send(pdfBuffer);
            } else if (file_type == 'csv') {
                const { mappedData } = processDataset(dataset)
                const buffer: any = await generateFile(file_type, mappedData)
                // Send the buffer in the response for file download
                res.setHeader('Content-Type', 'text/csv; charset=utf-8'); // Ensure the charset is specified
                res.setHeader('Content-Disposition', 'attachment; filename="report.csv"');
                res.setHeader('Content-Length', buffer.length.toString());
                res.end(buffer);
            }
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const splitData = (data: any, columnsPerTable: any) => {
    const tables = [];
    const numTables = Math.ceil(data.columns_group.length / columnsPerTable);

    for (let i = 0; i < numTables; i++) {
        const startIndex = i * columnsPerTable;
        const endIndex = Math.min((i + 1) * columnsPerTable, data.columns_group.length);

        const tableData = {
            columns_group: data.columns_group.slice(startIndex, endIndex),
            value: data.data.map((row: any) => {
                const tableRow: any = {};
                for (let j = startIndex; j < endIndex; j++) {
                    if (data?.columns_group[j]?.children?.length > 0) {
                        data?.columns_group[j]?.children?.forEach((child: any) => {
                            const key = child.key;
                            tableRow[key] = row[key];
                        });
                        continue
                    } else {
                        const key = data.columns_group[j].key;
                        tableRow[key] = row[key];
                    }
                }
                return tableRow;
            })
        };

        tables.push(tableData);
    }

    return tables;
};


const processDataset = (dataset: any) => {
    const data = dataset.data; // Row data
    const flattenedData = [...data, dataset.total]; // Include totals at the end
    const columnsGroup = dataset.columns_group;

    // Prepare headers and mapped data
    const headers: string[] = [];
    const mappedData: any[] = [];

    // Flatten columns and construct headers
    columnsGroup.forEach((group: any) => {
        if (group.type === "Group") {
            group.children.forEach((child: any) => {
                const columnLabel = `${group.content} - ${child.content}`;
                headers.push(columnLabel);
            });
        } else {
            const columnLabel = group.content;
            headers.push(columnLabel);
        }
    });

    // Map rows to the correct format based on column keys and groupings
    flattenedData.forEach((row: any) => {
        const newRow: any = {};

        columnsGroup.forEach((group: any) => {
            if (group.type === "Group") {
                group.children.forEach((child: any) => {
                    const key = child.key;
                    let columnLabel = `${group.content} - ${child.content}`;
                    if (columnLabel in newRow) {
                        columnLabel = `${group.content} - ${child.content} `
                    }
                    newRow[columnLabel] = row[key] !== undefined ? row[key] : ''// Use your preferred default value;
                });
            } else if (group.type === "text" || group.type === "total") {
                const key = group.key;
                let columnLabel = group.content;
                if (columnLabel in newRow) {
                    columnLabel = `${group.content} `
                }
                if (group.type === "total") {
                    // Calculate total based on the provided `totalColumns`
                    const total = group.columns.reduce((sum: any, col: any) => {
                        const value = row[col] !== undefined ? row[col] : 0;
                        return sum + parseFloat(value);
                    }, 0).toFixed(2);
                    newRow[columnLabel] = total;
                } else {
                    newRow[columnLabel] = row[key] !== undefined ? row[key] : '' // Use your preferred default value;
                }
            }
        });

        mappedData.push(newRow);
    });

    return { headers, mappedData };
};

function getColumnLetter(colIndex: any) {
    let letter = '';
    while (colIndex > 0) {
        const mod = (colIndex - 1) % 26;
        letter = String.fromCharCode(65 + mod) + letter;
        colIndex = Math.floor((colIndex - 1) / 26);
    }
    return letter;
}
const addWsrDetail = async (req: Request, res: Response) => {
    try {
        let findRole: any;

        // Determine the user's role based on the platform type from the request headers
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.web_user_active_role_id ? req.user.web_user_active_role_id : req.user.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && req.user.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.user_active_role_id }, raw: true });
        }

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'wsr', // WSR module slug
            ROLE_PERMISSIONS.CREATE
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.DIRECTOR
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = findRole && [
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER,
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.DIRECTOR
        ].includes(findRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        // Destructure required fields from the request body
        const { wsr_start_date, wsr_end_date, branch_id, data = [], current_datetime, wsr_amount_total = {}, wsr_remark = [] } = req.body;

        // Parse start date and current datetime into moment objects
        const collectionDateMoment = moment(wsr_start_date);
        const currentMoment = moment(current_datetime);

        // Calculate the next two weeks from the current date for validation
        const nextTwoWeeksStart = currentMoment.clone().subtract(1, 'weeks').startOf('week').add(1, 'day');
        const nextTwoWeeksEnd = currentMoment.clone().subtract(1, 'weeks').endOf('week').add(1, 'day').endOf('day');

        // Check if the branch exists and is active
        const findBranch = await Branch.findOne({ attributes: ['id', 'branch_name'], where: { id: branch_id, organization_id: req.user.organization_id, branch_status: { [Op.not]: branch_status.DELETED } }, raw: true });
        if (!findBranch) {
            return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
        }

        // Check if the WSR detail already exists for the given branch and dates
        const getWsrDetail = await WsrDetail.findOne({ attributes: ['id'], where: { branch_id: branch_id, wsr_start_date, wsr_end_date, wsr_detail_status: wsr_detail_status.ACTIVE }, raw: true });
        if (getWsrDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__("WSR_ALREADY_ADDED") });
        }

        // Role-specific validation for Branch Manager or Hotel Manager
        if (findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER) {
            // Validate if the WSR dates are within the next two weeks
            if (nextTwoWeeksStart.isAfter(collectionDateMoment) || nextTwoWeeksEnd.isAfter(moment(wsr_end_date).endOf('day'))) {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FAIL_CANNOT_ADD_BEYOND_TWO_WEEKS") });
            }
            // Prevent future dates for the WSR start date
            if (collectionDateMoment.isAfter(currentMoment)) {
                return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FAIL_CANNOT_ADD_FUTURE_WEEKS") });
            }
        }

        // Create the WSR detail record in the database
        const wsrDetail = await WsrDetail.setHeaders(req).create({
            user_id: req.user.id,
            branch_id: branch_id,
            wsr_start_date,
            wsr_end_date,
            wsr_detail_status: wsr_detail_status.ACTIVE,
            wsr_amount_total: JSON.stringify(wsr_amount_total),
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        if (wsrDetail) {
            // Process the associated data if present
            if (data && data.length > 0) {
                for (const item of data) {
                    const findWsrRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: wsrDetail.id, payment_type_id: item.id }, raw: true });
                    if (findWsrRemark) {
                        await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: wsrDetail.id, payment_type_id: item.id } });
                    } else {
                        await PaymentTypeRemark.setHeaders(req).create({
                            detail_id: wsrDetail.id,
                            payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                            payment_type_remark: item.payment_type_remark,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any);
                    }
                    if (item?.payment_type_category.length > 0) {
                        for (const category of item.payment_type_category) {
                            // Handle multiple payment type categories
                            if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                for (const option of category.categoryBranchValue) {
                                    if (option?.wsr_amount) {
                                        // Check if the document category branch exists
                                        const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                            attributes: ['id'],
                                            where: {
                                                id: option?.reference_id,
                                                branch_id: branch_id,
                                                payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                            }, raw: true
                                        });
                                        // Create WSR items if they don't already exist
                                        if (findDocumentCategoryBranch) {
                                            const findWsrItem = await WsrItem.findOne({ attributes: ['id'], where: { wsr_detail_id: wsrDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                            if (!findWsrItem) {
                                                await WsrItem.setHeaders(req).create({
                                                    wsr_detail_id: wsrDetail.id,
                                                    payment_type_category_id: category.payment_type_category_id,
                                                    wsr_amount: option.wsr_amount,
                                                    reference_id: option.reference_id,
                                                    wsr_item_status: wsr_item_status.ACTIVE,
                                                    created_by: req.user.id,
                                                    updated_by: req.user.id
                                                } as any);
                                            }
                                        }
                                    }
                                }
                            } else {
                                if (category?.wsr_amount) {
                                    // Handle single payment type category
                                    const findWsrItem = await WsrItem.findOne({ attributes: ['id'], where: { wsr_detail_id: wsrDetail.id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                    if (!findWsrItem) {
                                        await WsrItem.setHeaders(req).create({
                                            wsr_detail_id: wsrDetail.id,
                                            payment_type_category_id: category.payment_type_category_id,
                                            wsr_amount: category.wsr_amount,
                                            wsr_item_status: wsr_item_status.ACTIVE,
                                            created_by: req.user.id,
                                            updated_by: req.user.id
                                        } as any);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (wsr_remark.length > 0) {
                for (const remark of wsr_remark) {
                    await PaymentTypeRemark.setHeaders(req).create({
                        detail_id: wsrDetail.id,
                        payment_type_id: remark.payment_type_id, // TODO: CHECK IF THIS IS CORRECTL ,
                        payment_type_remark: remark.payment_type_remark,
                        created_by: req.user.id,
                        updated_by: req.user.id
                    } as any);
                }
            }

            // Respond indicating successful WSR addition
            return res.status(StatusCodes.OK).json({ status: true, message: res.__("WSR_ADDED_SUCCESSFULLY") });
        } else {
            // Handle failure to add WSR
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FAIL_TO_ADD_WSR") });
        }
    } catch (error) {
        console.log(error);
        // Handle unexpected errors
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const updateWsrDetail = async (req: Request, res: Response) => {
    try {
        let findRole: any;

        // Determine user role based on platform type
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.web_user_active_role_id ? req.user.web_user_active_role_id : req.user.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && req.user.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: req.user?.user_active_role_id }, raw: true });
        }

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'wsr', // WSR module slug
            ROLE_PERMISSIONS.EDIT
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.DIRECTOR
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = findRole && [
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER,
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.DIRECTOR
        ].includes(findRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        // Extract data from the request body and parameters
        const { data = [], current_datetime, wsr_amount_total, wsr_start_date, wsr_end_date, old_wsr_amount_total } = req.body;
        const { wsr_detail_id } = req.params;

        // Find existing WSR detail
        const getWsrDetail: any = await WsrDetail.findOne({ attributes: ['id', 'branch_id', 'wsr_end_date'], where: { id: wsr_detail_id, wsr_detail_status: wsr_detail_status.ACTIVE }, raw: true });

        if (getWsrDetail) {
            if (findRole?.role_name == ROLE_CONSTANT.SUPER_ADMIN || findRole?.role_name == ROLE_CONSTANT.ADMIN || findRole?.role_name == ROLE_CONSTANT.DIRECTOR || findRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
                const findWsrBasedDate = await WsrDetail.findOne({ attributes: ['id'], where: { wsr_start_date: wsr_start_date, wsr_end_date: wsr_end_date, wsr_detail_status: wsr_detail_status.ACTIVE, branch_id: getWsrDetail.branch_id }, raw: true });
                if (findWsrBasedDate) {
                    if (getWsrDetail.id != findWsrBasedDate?.id) {
                        return res.status(StatusCodes.BAD_REQUEST).json({
                            status: false,
                            message: res.__("WSR_ALREADY_EXISTS"),
                        })
                    }
                }
            }
            const wsrWeekEndDay = moment(getWsrDetail.wsr_end_date).endOf('day');
            const afterTwoWeeks = moment(wsrWeekEndDay).add(1, 'weeks').endOf('day'); // Calculate two weeks after WSR end
            const currentMoment: any = moment(current_datetime); // Current date and time
            const getFullName = await getUserFullName(req.user.id);

            // Check if user is a branch or hotel manager and if current time is valid for updating
            if (findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER) {
                if (currentMoment.isAfter(afterTwoWeeks)) {
                    // Check for existing pending request1
                    const findRequest = await WsrRequest.findOne({ attributes: ['id'], where: { wsr_detail_id: getWsrDetail.id, wsr_request_status: wsr_request_status.PENDING }, raw: true });
                    if (findRequest) {
                        return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("WSR_UPDATED_REQUEST_EXIST") });
                    }

                    // Create a new WSR request
                    const wsrRequest = await WsrRequest.setHeaders(req).create({
                        wsr_detail_id: getWsrDetail.id,
                        user_id: req.user.id,
                        wsr_request_status: request_status.PENDING,
                        wsr_amount_total: JSON.stringify(wsr_amount_total),
                        old_wsr_amount_total: JSON.stringify(old_wsr_amount_total),
                        created_by: req.user.id,
                        updated_by: req.user.id
                    } as any);

                    if (wsrRequest) {
                        // Notify users about the WSR update request
                        const findUsers = (await User.findAll({
                            attributes: ['id', 'appToken', 'webAppToken'],
                            where: {
                                user_status: {
                                    [Op.not]: [user_status.DELETED, user_status.PENDING, user_status.CANCELLED],
                                },
                                id: {
                                    [Op.in]: [
                                        sequelize.literal(`(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${ROLE_CONSTANT.SUPER_ADMIN}','${ROLE_CONSTANT.ADMIN}','${ROLE_CONSTANT.DIRECTOR}','${ROLE_CONSTANT.ACCOUNTANT}')))`)
                                    ],
                                },
                                organization_id: req.user.organization_id
                            },
                            group: ['id'],
                            raw: true
                        })) || [];

                        // Fetch branch details for notification
                        const findBranch = await Branch.findOne({ attributes: ['id', 'branch_name'], where: { id: getWsrDetail.branch_id, organization_id: req.user.organization_id }, raw: true });
                        await createNotification(findUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.WSR_UPDATE_REQUEST.content(getFullName, findBranch?.branch_name, moment(getWsrDetail?.wsr_start_date).format("DD/MM/YYYY"), moment(getWsrDetail?.wsr_end_date).format("DD/MM/YYYY")), NOTIFICATIONCONSTANT.WSR_UPDATE_REQUEST.heading, REDIRECTION_TYPE.WSR, getWsrDetail.id, { wsr_id: getWsrDetail.id });

                        return res.status(StatusCodes.OK).json({ status: true, message: res.__("WSR_REQUEST_ADDED") });
                    } else {
                        return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FAIL_TO_ADD_WSR_REQUEST") });
                    }
                }
            }

            // Process items if data is provided
            if (data && data.length > 0) {
                const wsrItemIds: any = [];
                for (const item of data) {
                    const findWsrRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: wsr_detail_id, payment_type_id: item.id }, raw: true });
                    if (findWsrRemark) {
                        await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: wsr_detail_id, payment_type_id: item.id } });
                    } else {
                        await PaymentTypeRemark.setHeaders(req).create({
                            detail_id: wsr_detail_id,
                            payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                            payment_type_remark: item.payment_type_remark,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any);
                    }
                    if (item?.payment_type_category.length > 0) {
                        for (const category of item.payment_type_category) {
                            // Handle multiple payment type categories
                            if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                for (const option of category.categoryBranchValue) {
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: option?.reference_id,
                                            branch_id: getWsrDetail.branch_id,
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    });

                                    if (findDocumentCategoryBranch) {
                                        if (option?.wsr_amount) {
                                            if (option.wsr_item_id) {
                                                // Update existing WSR item
                                                const findWsrItem = await WsrItem.findOne({ attributes: ['id'], where: { id: option.wsr_item_id }, raw: true });
                                                if (findWsrItem) {
                                                    wsrItemIds.push(findWsrItem.id);
                                                    await WsrItem.setHeaders(req).update({ wsr_amount: option.wsr_amount, wsr_item_status: wsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: option.wsr_item_id } });
                                                } else {
                                                    // Create new WSR item if it does not exist
                                                    const findWsrItem = await WsrItem.findOne({ attributes: ['id'], where: { wsr_detail_id: getWsrDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                                    if (!findWsrItem) {
                                                        const createItem = await WsrItem.setHeaders(req).create({
                                                            wsr_detail_id: getWsrDetail.id,
                                                            payment_type_category_id: category.payment_type_category_id,
                                                            wsr_amount: option.wsr_amount,
                                                            reference_id: option.reference_id,
                                                            wsr_item_status: wsr_item_status.ACTIVE,
                                                            created_by: req.user.id,
                                                            updated_by: req.user.id
                                                        } as any);
                                                        if (createItem) wsrItemIds.push(createItem.id);
                                                    }
                                                }
                                            } else {
                                                const findWsrItem = await WsrItem.findOne({ attributes: ['id'], where: { wsr_detail_id: getWsrDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                                if (!findWsrItem) {
                                                    const createItem = await WsrItem.setHeaders(req).create({
                                                        wsr_detail_id: getWsrDetail.id,
                                                        payment_type_category_id: category.payment_type_category_id,
                                                        wsr_amount: option.wsr_amount,
                                                        reference_id: option.reference_id,
                                                        wsr_item_status: wsr_item_status.ACTIVE,
                                                        created_by: req.user.id,
                                                        updated_by: req.user.id
                                                    } as any);
                                                    if (createItem) {
                                                        wsrItemIds.push(createItem.id);
                                                    }

                                                } else {
                                                    wsrItemIds.push(findWsrItem.id)
                                                    await WsrItem.setHeaders(req).update({ wsr_amount: option.wsr_amount, wsr_item_status: wsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findWsrItem.id } })
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                // Handle single payment type categories
                                if (category.wsr_amount) {
                                    if (category.wsr_item_id) {
                                        // Update existing WSR item
                                        const findWsrItemRequest = await WsrItem.findOne({ attributes: ['id'], where: { id: category.wsr_item_id }, raw: true });
                                        if (findWsrItemRequest) {
                                            wsrItemIds.push(findWsrItemRequest.id);
                                            await WsrItem.setHeaders(req).update({ wsr_amount: category.wsr_amount, wsr_item_status: wsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: category.wsr_item_id } });
                                        }
                                    } else {
                                        // Create new WSR item if it does not exist
                                        const findWsrItemRequest = await WsrItem.findOne({ attributes: ['id'], where: { wsr_detail_id: getWsrDetail.id, payment_type_category_id: category.payment_type_category_id }, raw: true });
                                        if (!findWsrItemRequest) {
                                            const createItem = await WsrItem.setHeaders(req).create({
                                                wsr_detail_id: getWsrDetail.id,
                                                payment_type_category_id: category.payment_type_category_id,
                                                wsr_amount: category.wsr_amount,
                                                wsr_item_status: wsr_item_status.ACTIVE,
                                                created_by: req.user.id,
                                                updated_by: req.user.id
                                            } as any);
                                            if (createItem) wsrItemIds.push(createItem.id);
                                        } else {
                                            wsrItemIds.push(findWsrItemRequest.id);
                                            await WsrItem.setHeaders(req).update({ wsr_amount: category.wsr_amount, wsr_item_status: wsr_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findWsrItemRequest.id } });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // Deactivate items that are not in the current request
                await WsrItem.setHeaders(req).update({ wsr_item_status: wsr_item_status.INACTIVE }, { where: { wsr_detail_id: getWsrDetail.id, id: { [Op.notIn]: wsrItemIds }, wsr_item_status: wsr_item_status.ACTIVE } });
                // Delete DSR items not in the updated list
                await WsrItem.destroy({ where: { wsr_detail_id: getWsrDetail.id, id: { [Op.notIn]: wsrItemIds } } });
                // Update the total amount for the WSR detail
                const updateWsrDetail: any = {
                    wsr_amount_total: JSON.stringify(wsr_amount_total),
                    updated_by: req.user.id
                }
                // Only super admin, admin, director, accountant can update WSR date
                if (findRole?.role_name == ROLE_CONSTANT.SUPER_ADMIN || findRole?.role_name == ROLE_CONSTANT.ADMIN || findRole?.role_name == ROLE_CONSTANT.DIRECTOR || findRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
                    updateWsrDetail.wsr_start_date = wsr_start_date
                    updateWsrDetail.wsr_end_date = wsr_end_date
                }
                // Update the total amount for the DSR detail
                await WsrDetail.setHeaders(req).update(updateWsrDetail, { where: { id: wsr_detail_id } });
            }
            return res.status(StatusCodes.OK).json({ status: true, message: res.__("WSR_UPDATED_SUCCESSFULLY") });
        }
    } catch (error) {
        // Handle any unexpected errors
        console.error(error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ status: false, message: res.__("SOMETHING_WENT_WRONG") });
    }
};


const getWsrById = async (req: Request, res: Response) => {
    try {
        // Extract the wsr_detail_id from request parameters
        const { wsr_detail_id }: any = req.params;

        // Fetch the WSR detail record with associated user and branch details
        const findWsrDetail: any = await WsrDetail.findOne({
            include: [
                {
                    model: User,
                    as: "wsr_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "wsr_branch",
                    attributes: [
                        "id",
                        "branch_name"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                }
            ],
            where: { id: wsr_detail_id },
            raw: true,
            nest: true
        });

        // Check if the WSR detail was found
        if (findWsrDetail) {
            // Define base query conditions for fetching payment types
            const whereObj: any = {
                organization_id: req.user.organization_id,
                payment_type_usage: { [Op.in]: [payment_type_usage.COLLECTION, payment_type_usage.OTHER] },
                has_weekly_use: true
            };

            // Add conditions for active payment types or those associated with the specific WSR detail
            whereObj[Op.or] = [
                { payment_type_status: paymentTypeStatus.ACTIVE },
                {
                    id: {
                        [Op.in]: sequelize.literal(`(SELECT pt.id
                            FROM nv_wsr_items AS di 
                            JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
                            JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
                            WHERE di.wsr_detail_id = ${wsr_detail_id}
                            GROUP BY pt.id
                        )`)
                    }
                }
            ];

            // Fetch Payment Type Details based on defined conditions
            let getPaymentTypeDetails: any = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency', [sequelize.literal(`( 
                    SELECT payment_type_remark
                        FROM nv_payment_type_remarks AS pyr
                        WHERE pyr.detail_id = ${findWsrDetail.id} AND pyr.payment_type_id = PaymentType.id )
                    `), 'payment_type_remark']],
                include: [
                    {
                        model: PaymentTypeCategory,
                        as: "payment_type_category",
                        attributes: [
                            ['id', 'payment_type_category_id'],
                            'payment_type_category_remarks',
                            'payment_type_category_title',
                            'payment_type_category_status',
                            'payment_type_category_pattern',
                            'payment_type_category_order',
                            [sequelize.literal(`(SELECT id 
                                FROM nv_payment_type_category_branch 
                                WHERE branch_id = ${findWsrDetail.branch_id} 
                               -- AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                AND parent_id is null 
                                AND has_default_active = 1
                                AND payment_type_category_id = payment_type_category.id
                            )`), 'payment_type_category_branch_id'],
                            [sequelize.literal(`( 
                                SELECT di.wsr_amount 
                                    FROM nv_wsr_items AS di 
                                    WHERE di.wsr_detail_id = ${findWsrDetail.id} AND di.payment_type_category_id = payment_type_category.id AND di.wsr_item_status = '${wsr_item_status.ACTIVE}' AND  di.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'wsr_amount'],
                            [sequelize.literal(`( 
                                SELECT di.id 
                                    FROM nv_wsr_items AS di 
                                    WHERE di.wsr_detail_id = ${findWsrDetail.id} AND di.payment_type_category_id = payment_type_category.id AND di.wsr_item_status = '${wsr_item_status.ACTIVE}' AND  di.reference_id IS NULL)
                                `), 'wsr_item_id']
                        ],
                        where: {
                            [Op.or]: [
                                {
                                    payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT payment_type_category_id 
                                                FROM nv_payment_type_category_branch 
                                                WHERE branch_id = ${findWsrDetail.branch_id} 
                                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND parent_id is null 
                                                AND has_default_active = 1)`)
                                        ]
                                    },
                                },
                                {
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT ptc.id
                                                FROM nv_wsr_items AS di 
                                                JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
                                                WHERE di.wsr_detail_id = ${wsr_detail_id}
                                                GROUP BY ptc.id
                                            )`)
                                        ]
                                    }
                                }
                            ]
                        },
                    }
                ],
                where: whereObj,
                order: [
                    ['payment_type_usage', 'ASC'],
                    ['payment_type_order', 'ASC'],
                    [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
                ],
            });

            // If payment types exist, process the categories and fetch their branches
            if (getPaymentTypeDetails.length > 0) {
                // Convert data to JSON format for easier manipulation
                getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

                // Loop through each payment type's category
                for (const paymentType of getPaymentTypeDetails) {
                    if (paymentType?.payment_type_category?.length > 0) {
                        for (const category of paymentType.payment_type_category) {

                            // Fetch category branches for each category
                            const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                                attributes: [
                                    ['id', 'reference_id'],
                                    [sequelize.literal(`(SELECT nv_payment_type_category_value.field_value 
                                        FROM nv_payment_type_category_value 
                                        INNER JOIN nv_payment_type_category_field 
                                        ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                        WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                        AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                        -- AND nv_payment_type_category_field.payment_type_category_id = ${category.payment_type_category_id}
                                        ORDER BY nv_payment_type_category_value.createdAt ASC 
                                        LIMIT 1
                                    )`), 'first_field_value'],
                                    [sequelize.literal(`( 
                                    SELECT di.wsr_amount 
                                        FROM nv_wsr_items AS di 
                                        WHERE di.wsr_detail_id = ${findWsrDetail.id} AND di.payment_type_category_id = ${category.payment_type_category_id} AND di.wsr_item_status = '${wsr_item_status.ACTIVE}' AND di.reference_id = PaymentTypeCategoryBranch.id 
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'wsr_amount'],
                                    [sequelize.literal(`( 
                                        SELECT di.id 
                                        FROM nv_wsr_items AS di 
                                        WHERE di.wsr_detail_id = ${findWsrDetail.id} AND di.payment_type_category_id = ${category.payment_type_category_id} AND di.wsr_item_status = '${wsr_item_status.ACTIVE}' AND di.reference_id = PaymentTypeCategoryBranch.id )
                                        `), 'wsr_item_id']
                                ],
                                where: {
                                    branch_id: findWsrDetail.branch_id,
                                    parent_id: category.payment_type_category_branch_id,
                                    payment_type_category_id: category.payment_type_category_id,
                                    [Op.or]: [
                                        {
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                            has_default_active: 1
                                        }, {
                                            id: {
                                                [Op.in]: [sequelize.literal(`(SELECT ptcb.id
                                                    FROM nv_wsr_items AS di 
                                                    LEFT JOIN nv_payment_type_category_branch AS ptcb ON ptcb.id = di.reference_id
                                                    WHERE di.wsr_detail_id = ${wsr_detail_id} AND di.payment_type_category_id = ${category.payment_type_category_id}
                                                    GROUP BY ptcb.id
                                                )`)]
                                            }
                                        }
                                    ]
                                },
                                order: [['payment_type_category_branch_order', 'ASC']],
                            });
                            // Assign found branch value to category
                            category.categoryBranchValue = findCategoryBranch;
                        }
                    }
                }
            }

            // Attach payment type details to the WSR detail
            findWsrDetail.wsrItems = getPaymentTypeDetails;

            // Send response with WSR detail and payment type data
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: findWsrDetail,
                vat_per_data: global.config.VAT_PER_DATA
            });
        } else {
            // If no WSR detail was found, return a bad request response
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_DATA_NOT_FOUND"),
                data: {}
            });
        }
    } catch (error) {
        // Handle any errors that occur during the execution of the function
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getWsrList = async (req: Request, res: Response) => {
    try {
        // Extracting query parameters from the request
        const { page, size, search, branch_id, wsr_start_date, wsr_end_date }: any = req.query;

        // Calculating pagination limits and offsets
        const { limit, offset } = getPagination(page, size);

        // Fetching user details from the database
        const userDetail: any = await User.findOne({ attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true });

        let findRole;
        // Determining user role based on platform type
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: userDetail.web_user_active_role_id ? userDetail.web_user_active_role_id : userDetail.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && userDetail.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: userDetail.user_active_role_id }, raw: true });
        }

        const whereObj = {
            organization_id: req.user.organization_id
        };

        // Constructing the query object for WSR details
        const wsrObj: any = {
            where: { wsr_detail_status: { [Op.not]: wsr_detail_status.DELETED } }, // Excluding deleted WSR details
            attributes: ['id', 'wsr_start_date', 'wsr_end_date', 'wsr_amount_total', 'wsr_detail_status',
                // Calculating total amount of active WSR items
                [
                    sequelize.literal(`
                      IF(
                        (
                          SELECT ROUND(SUM(wsr_amount), 2)
                          FROM nv_wsr_items AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.wsr_detail_id = WsrDetail.id
                          AND items.wsr_item_status = '${wsr_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ) IS NOT NULL,
                        (
                          SELECT ROUND(SUM(wsr_amount), 2)
                          FROM nv_wsr_items AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.wsr_detail_id = WsrDetail.id
                          AND items.wsr_item_status = '${wsr_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ),
                        0
                      )
                    `),
                    'amount'
                ],
                // Checking if there are any active requests associated with the WSR detail
                [sequelize.literal(`EXISTS (SELECT 1 FROM nv_wsr_requests WHERE nv_wsr_requests.wsr_detail_id = WsrDetail.id AND wsr_request_status != '${wsr_request_status.DELETED}')`), 'has_request']
            ],
            include: [
                {
                    model: User,
                    as: "wsr_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("wsr_user.user_first_name"),
                                " ",
                                sequelize.col("wsr_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "wsr_detail_updated_by",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("wsr_detail_updated_by.user_first_name"),
                                " ",
                                sequelize.col("wsr_detail_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "wsr_branch",
                    attributes: [
                        "id",
                        "branch_name",
                        "branch_color",
                        "text_color"
                    ],
                    where: whereObj // Including where conditions for branches
                },
            ],
            order: [['wsr_start_date', 'DESC'], ['wsr_end_date', 'DESC']], // Ordering WSRs by start and end date
        };

        // Adding pagination limits if page and size are provided
        if (page && size) {
            wsrObj.limit = Number(limit);
            wsrObj.offset = Number(offset);
        }

        // Filtering by branch ID if provided
        if (branch_id) {
            wsrObj.where.branch_id = branch_id;
        }

        // Implementing search functionality
        if (search) {
            wsrObj.where[Op.or] = [
                { '$wsr_user.user_first_name$': { [Op.like]: `%${search}%` } },
                { '$wsr_user.user_last_name$': { [Op.like]: `%${search}%` } },
                { '$wsr_branch.branch_name$': { [Op.like]: `%${search}%` } }
            ];
        }

        // Restricting results to user's branch for certain roles
        if ((findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER) || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER) {
            wsrObj.where.branch_id = userDetail?.branch_id;
        }

        // Filtering by start and end date if provided
        if (wsr_start_date && wsr_end_date) {
            wsrObj.where.wsr_start_date = { [Op.lte]: wsr_end_date };
            wsrObj.where.wsr_end_date = { [Op.gte]: wsr_start_date };
        }

        // Fetching the WSR list with pagination and count
        const { count, rows: getWsrList } = await WsrDetail.findAndCountAll(wsrObj);

        // Calculating total pages for pagination
        const { total_pages } = getPaginatedItems(size, page, count || 0);

        // Responding with the fetched data
        return res.status(StatusCodes.OK).json({
            status: true,
            data: getWsrList,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });
    } catch (error) {
        console.log(error);
        // Handling errors and responding accordingly
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const deleteWsrById = async (req: Request, res: Response) => {
    try {
        // Extracting WSR detail ID from request parameters
        const { wsr_detail_id }: any = req.params;

        // Finding the WSR detail by ID
        const findWsrDetail = await WsrDetail.findOne({ attributes: ['id'], where: { id: wsr_detail_id }, raw: true });

        // If WSR detail not found, return an error response
        if (!findWsrDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_WSR_NOT_FOUND"),
            });
        }

        // Updating the WSR detail status to DELETED
        const deleteWsr = await WsrDetail.setHeaders(req).update({ wsr_detail_status: wsr_detail_status.DELETED }, { where: { id: wsr_detail_id } });

        // If the update was successful, update associated WSR items to DELETED status
        if (deleteWsr.length > 0) {
            await WsrItem.setHeaders(req).update({ wsr_item_status: wsr_item_status.DELETED }, { where: { wsr_detail_id: wsr_detail_id } });

            // Return success response
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("WSR_DELETED_SUCCESSFULLY"),
            });
        } else {
            // If update failed, return an error response
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAILED_TO_DELETE_WSR"),
            });
        }
    } catch (error) {
        console.log(error);
        // Handle unexpected errors and return a service unavailable response
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getWsrRequestList = async (req: Request, res: Response) => {
    try {
        // Extracting query parameters
        const { page, size, search, branch_id, wsr_id, wsr_start_date, wsr_end_date, request_status }: any = req.query;
        const { limit, offset } = getPagination(page, size);
        const branchWhereObj: any = {
            organization_id: req.user.organization_id
        };

        // Fetching user details based on the authenticated user
        const userDetail: any = await User.findOne({ attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'branch_id'], where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true });
        let findRole;

        // Determining user role based on platform type
        if (req.headers["platform-type"] == "web") {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: userDetail.web_user_active_role_id ? userDetail.web_user_active_role_id : userDetail.user_active_role_id }, raw: true });
        } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && userDetail.user_active_role_id) {
            findRole = await Role.findOne({ attributes: ['id', 'role_name'], where: { id: userDetail.user_active_role_id }, raw: true });
        }

        // Filtering by branch ID if provided
        if (branch_id) {
            branchWhereObj.id = branch_id;
        }

        // If the user is a Branch Manager or Hotel Manager, filter by user's branch
        if ((findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER) || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER) {
            branchWhereObj.id = userDetail?.branch_id;
        }

        // Preparing WSR filtering criteria
        const wsrWhereObj: any = {};
        if (wsr_start_date && wsr_end_date) {
            wsrWhereObj.where.wsr_start_date = { [Op.lte]: wsr_end_date };
            wsrWhereObj.where.wsr_end_date = { [Op.gte]: wsr_start_date };
        }

        // Filter by WSR ID if provided
        if (wsr_id) {
            wsrWhereObj.id = wsr_id;
        }

        // Constructing the WSR request query
        const wsrObj: any = {
            where: { wsr_request_status: { [Op.not]: wsr_request_status.DELETED } },
            attributes: [
                'id',
                'wsr_detail_id',
                'wsr_detail.branch_id',
                'wsr_detail.wsr_start_date',
                'wsr_detail.wsr_end_date',
                'wsr_detail.wsr_detail_status',
                'wsr_request_status',
                'wsr_amount_total',
                // Calculating total amount for active items
                [
                    sequelize.literal(`
                      IF(
                        (
                          SELECT ROUND(SUM(wsr_amount), 2)
                          FROM nv_wsr_item_requests AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.wsr_request_detail_id = WsrRequest.id
                          AND items.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ) IS NOT NULL,
                        (
                           SELECT ROUND(SUM(wsr_amount), 2)
                          FROM nv_wsr_item_requests AS items
                          INNER JOIN nv_payment_type_category AS ptc
                            ON items.payment_type_category_id = ptc.id
                          INNER JOIN nv_payment_type AS pt
                            ON ptc.payment_type_id = pt.id
                          WHERE items.wsr_request_detail_id = WsrRequest.id
                          AND items.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}'
                          AND pt.has_include_amount = true
                        ),
                        0
                      )
                    `),
                    'amount'
                ]
            ],
            include: [
                {
                    model: WsrDetail,
                    as: "wsr_detail",
                    attributes: [
                        "id",
                        "branch_id",
                        "wsr_start_date",
                        "wsr_end_date",
                        "wsr_detail_status"
                    ],
                    where: wsrWhereObj,
                    include: [
                        {
                            model: Branch,
                            as: "wsr_branch",
                            attributes: [
                                "id",
                                "branch_name",
                                "branch_color",
                                "text_color"
                            ],
                            where: branchWhereObj
                        }
                    ]
                },
                {
                    model: User,
                    as: "wsr_request_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("wsr_request_user.user_first_name"),
                                " ",
                                sequelize.col("wsr_request_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "wsr_request_updated_by",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("wsr_request_updated_by.user_first_name"),
                                " ",
                                sequelize.col("wsr_request_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                }
            ],
            order: [['updatedAt', 'DESC']],
            raw: true,
            nest: true
        };

        // Adding pagination if page and size are provided
        if (page && size) {
            wsrObj.limit = Number(limit);
            wsrObj.offset = Number(offset);
        }

        // Adding search functionality
        if (search) {
            wsrObj.where[Op.or] = [
                { '$wsr_request_user.user_first_name$': { [Op.like]: `%${search}%` } },
                { '$wsr_request_user.user_last_name$': { [Op.like]: `%${search}%` } },
                { '$wsr_detail.wsr_branch.branch_name$': { [Op.like]: `%${search}%` } }
            ];
        }

        // Filtering by request status if provided
        if (request_status) {
            wsrObj.where.wsr_request_status = request_status;
        }

        // Fetching WSR requests and their count
        const { count, rows: getWsrList } = await WsrRequest.findAndCountAll(wsrObj);

        // Transforming the fetched data for response
        const transformedData = getWsrList.map((item: any) => {
            return {
                id: item.id,
                wsr_start_date: item.wsr_detail.wsr_start_date,
                wsr_end_date: item.wsr_detail.wsr_end_date,
                wsr_detail_status: item.wsr_detail.wsr_detail_status,
                wsr_request_status: item.wsr_request_status,
                amount: item.amount,
                wsr_user: {
                    id: item.wsr_request_user.id,
                    user_full_name: item.wsr_request_user.user_full_name
                },
                wsr_branch: {
                    id: item.wsr_detail.wsr_branch.id,
                    branch_name: item.wsr_detail.wsr_branch.branch_name,
                    branch_color: item.wsr_detail.wsr_branch.branch_color,
                    text_color: item.wsr_detail.wsr_branch.text_color
                },
                wsr_amount_total: item.wsr_amount_total,
                wsr_request_updated_by: {
                    id: item.wsr_request_updated_by.id,
                    user_full_name: item.wsr_request_updated_by.user_full_name
                },
            };
        });

        // Getting pagination details for response
        const { total_pages } = getPaginatedItems(size, page, count || 0);

        // Sending successful response
        return res.status(StatusCodes.OK).json({
            status: true,
            data: transformedData,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });
    } catch (error) {
        console.log(error);
        // Handling errors and returning a service unavailable response
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getWsrRequestById = async (req: Request, res: Response) => {
    try {
        // Extracting the request ID from the request parameters
        const { wsr_request_id }: any = req.params;

        // Fetching the WSR request along with associated user and details
        const findWsrRequest: any = await WsrRequest.findOne({
            include: [
                {
                    model: User,
                    as: "wsr_request_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("wsr_request_user.user_first_name"),
                                " ",
                                sequelize.col("wsr_request_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "wsr_request_updated_by",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("wsr_request_updated_by.user_first_name"),
                                " ",
                                sequelize.col("wsr_request_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: WsrDetail,
                    as: "wsr_detail",
                    attributes: [
                        "id",
                        "branch_id", "wsr_start_date", "wsr_end_date", "wsr_detail_status"],
                    include: [{
                        model: Branch,
                        as: "wsr_branch",
                        attributes: [
                            "id",
                            "branch_name"],
                        where: {
                            organization_id: req.user.organization_id
                        }
                    }]
                }
            ],
            where: { id: wsr_request_id },
            raw: true,
            nest: true
        });

        // Check if the WSR request was found
        if (findWsrRequest) {
            // Define base query conditions for fetching payment types
            const whereObj: any = {
                organization_id: req.user.organization_id,
                payment_type_usage: { [Op.in]: [payment_type_usage.COLLECTION, payment_type_usage.OTHER] },
                has_weekly_use: true
            };

            // Adding OR conditions for payment type status
            whereObj[Op.or] = [
                { payment_type_status: paymentTypeStatus.ACTIVE },
                {
                    id: {
                        [Op.in]: sequelize.literal(`(SELECT pt.id
                            FROM nv_wsr_item_requests AS dir 
                            JOIN nv_payment_type_category AS ptc ON ptc.id = dir.payment_type_category_id
                            JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
                            WHERE dir.wsr_request_detail_id = ${findWsrRequest.id}
                            GROUP BY pt.id
                        )`)
                    }
                }
            ];

            // Fetching payment type details based on the defined conditions
            let getPaymentTypeDetails: any = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency', [sequelize.literal(`( 
                    SELECT payment_type_remark
                        FROM nv_payment_type_remarks AS pyr
                        WHERE pyr.detail_id = ${findWsrRequest?.wsr_detail?.id} AND pyr.payment_type_id = PaymentType.id )
                    `), 'payment_type_remark']],
                include: [
                    {
                        model: PaymentTypeCategory,
                        as: "payment_type_category",
                        attributes: [['id', 'payment_type_category_id'], 'payment_type_category_remarks', 'payment_type_category_title', 'payment_type_category_status', 'payment_type_category_pattern', 'payment_type_category_order', [sequelize.literal(`(SELECT id 
                            FROM nv_payment_type_category_branch 
                            WHERE branch_id = ${findWsrRequest?.wsr_detail?.branch_id} 
                           -- AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                            AND parent_id is null 
                            AND has_default_active = 1
                            AND payment_type_category_id = payment_type_category.id
                        )`), 'payment_type_category_branch_id'],
                        [sequelize.literal(`( 
                                SELECT dir.wsr_amount 
                                    FROM nv_wsr_item_requests AS dir 
                                    WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}' AND dir.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'wsr_amount'],
                        [sequelize.literal(`( 
                                SELECT dir.id 
                                    FROM nv_wsr_item_requests AS dir 
                                    WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}' AND dir.reference_id IS NULL)
                                `), 'wsr_request_item_id'],
                        [sequelize.literal(`( 
                                    SELECT dir.old_wsr_amount 
                                        FROM nv_wsr_item_requests AS dir 
                                        WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}' AND dir.reference_id IS NULL
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'old_wsr_amount']
                        ],
                        where: {
                            [Op.or]: [
                                {
                                    payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT payment_type_category_id 
                                                FROM nv_payment_type_category_branch 
                                                WHERE branch_id = ${findWsrRequest?.wsr_detail?.branch_id} 
                                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND parent_id is null 
                                                AND has_default_active = 1)`)
                                        ]
                                    },
                                },
                                {
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT ptc.id
                                                FROM nv_wsr_item_requests AS dir 
                                                JOIN nv_payment_type_category AS ptc ON ptc.id = dir.payment_type_category_id
                                                WHERE dir.wsr_request_detail_id = ${findWsrRequest.id}
                                                GROUP BY ptc.id
                                            )`)
                                        ]
                                    }
                                }
                            ]
                        },
                    }
                ],
                where: whereObj,
                order: [
                    ['payment_type_usage', 'ASC'],
                    ['payment_type_order', 'ASC'],
                    [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
                ],
            });

            // If payment types exist, process the categories and fetch their branches
            if (getPaymentTypeDetails.length > 0) {
                // Convert data to JSON format for easier manipulation
                getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

                // Loop through each payment type's category
                for (const paymentType of getPaymentTypeDetails) {
                    if (paymentType?.payment_type_category?.length > 0) {
                        for (const category of paymentType.payment_type_category) {
                            // Fetch category branches for each category
                            const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                                attributes: [
                                    ['id', 'reference_id'],
                                    [sequelize.literal(`
                                        (SELECT nv_payment_type_category_value.field_value 
                                        FROM nv_payment_type_category_value 
                                        INNER JOIN nv_payment_type_category_field 
                                        ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                        WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                        -- AND nv_payment_type_category_field.field_type = 'string' 
                                        AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                        ORDER BY nv_payment_type_category_value.createdAt ASC 
                                        LIMIT 1
                                    )`), 'first_field_value'],
                                    [sequelize.literal(`( 
                                    SELECT dir.wsr_amount 
                                        FROM nv_wsr_item_requests AS dir 
                                        WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id 
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'wsr_amount'],
                                    [sequelize.literal(`( 
                                        SELECT dir.id 
                                        FROM nv_wsr_item_requests AS dir
                                        WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id )
                                        `), 'wsr_item_id'],
                                    [sequelize.literal(`( 
                                            SELECT dir.old_wsr_amount 
                                                FROM nv_wsr_item_requests AS dir 
                                                WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.wsr_request_item_status = '${wsr_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id 
                                            UNION SELECT 0 LIMIT 1)
                                            `), 'old_wsr_amount']
                                ],
                                where: {
                                    branch_id: findWsrRequest?.wsr_detail?.branch_id,
                                    parent_id: category.payment_type_category_branch_id,
                                    payment_type_category_id: category.payment_type_category_id,
                                    [Op.or]: [
                                        {
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                            has_default_active: 1
                                        }, {
                                            id: {
                                                [Op.in]: [sequelize.literal(`(SELECT ptcb.id
                                                    FROM nv_wsr_item_requests AS dir 
                                                    LEFT JOIN nv_payment_type_category_branch AS ptcb ON ptcb.id = dir.reference_id
                                                    WHERE dir.wsr_request_detail_id = ${findWsrRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id}
                                                    GROUP BY ptcb.id
                                                )`)]
                                            }
                                        }
                                    ]
                                },
                                order: [['payment_type_category_branch_order', 'ASC']],
                            });

                            // Assign found branch value to category
                            category.categoryBranchValue = findCategoryBranch;
                        }
                    }
                }
            }

            // Respond with the found request details and associated data
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: {
                    id: findWsrRequest?.wsr_detail?.id,
                    wsr_start_date: findWsrRequest?.wsr_detail?.wsr_start_date,
                    wsr_end_date: findWsrRequest?.wsr_detail?.wsr_end_date,
                    wsr_detail_status: findWsrRequest?.wsr_detail?.wsr_detail_status,
                    request_remark: findWsrRequest.request_remark,
                    wsr_request_status: findWsrRequest.wsr_request_status,
                    wsr_branch: findWsrRequest?.wsr_detail?.wsr_branch?.branch_name,
                    wsr_branch_id: findWsrRequest?.wsr_detail?.wsr_branch?.id,
                    submitted_user: findWsrRequest?.wsr_request_user?.user_full_name,
                    wsr_items: getPaymentTypeDetails || [],
                    wsr_amount_total: findWsrRequest.wsr_amount_total,
                    wsr_request_updated_by: findWsrRequest?.wsr_request_updated_by,
                    old_wsr_amount_total: findWsrRequest?.old_wsr_amount_total
                },
                vat_per_data: global.config.VAT_PER_DATA
            });
        } else {
            // Handle case where the request ID was not found
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("WSR_REQUEST_NOT_FOUND"),
            });
        }
    } catch (error) {
        // Log any errors and respond with a server error message
        console.error("Error fetching WSR request:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
        });
    }
};


const approveRejectRequestWsr = async (req: Request, res: Response) => {
    try {
        // Extracting the request body parameters: WSR request ID, status, and remarks
        const { wsr_request_id, request_status, request_remark }: any = req.body;

        // Finding the WSR request detail that matches the provided ID and is still pending
        const findWsrDetail: any = await WsrRequest.findOne({
            attributes: ['id', 'wsr_detail_id', 'wsr_amount_total', 'created_by'],
            where: { id: wsr_request_id, wsr_request_status: wsr_request_status.PENDING }, raw: true
        });

        // If no WSR request detail is found, send an error response
        if (!findWsrDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_DSR_REQUEST_NOT_FOUND"),
            });
        }

        // Fetching the associated WSR detail using the found request's detail ID
        const findWsrDate: any = await WsrDetail.findOne({
            attributes: ['id', 'user_id', 'branch_id', 'wsr_start_date', 'wsr_end_date'],
            where: { id: findWsrDetail.wsr_detail_id }, raw: true
        });

        // Retrieving the user who made the WSR request
        const findUser = await User.findOne({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: { id: findWsrDate?.user_id }, raw: true
        });

        // Getting the full name of the user performing the action for notifications
        const getActionUserFullName: any = await getUserFullName(req.user.id);

        // Handling the approval case
        if (request_status == wsr_request_status.APPROVED) {
            // Updating the WSR request status to approved
            const updateWsrRequest = await WsrRequest.setHeaders(req).update({
                wsr_request_status: wsr_request_status.APPROVED,
                request_remark: request_remark,
                updated_by: req.user.id
            }, { where: { id: wsr_request_id } });

            // If the update was successful, proceed with additional updates
            if (updateWsrRequest.length > 0) {
                // Update the total amount in the WSR detail
                await WsrDetail.setHeaders(req).update({
                    wsr_amount_total: findWsrDetail.wsr_amount_total,
                    updated_by: req.user.id
                }, { where: { id: findWsrDate.id } });

                // Fetching the list of items associated with the WSR request
                const findWsrItemList = await WsrItemRequest.findAll({
                    attributes: ['id', 'payment_type_category_id', 'reference_id', 'wsr_amount'],
                    where: {
                        wsr_request_detail_id: findWsrDetail.id,
                        wsr_request_item_status: wsr_request_item_status.ACTIVE
                    }
                });

                // If there are items in the WSR request, process each one
                if (findWsrItemList.length > 0) {
                    const wsrItemIds = []; // Array to hold IDs of processed items

                    // Loop through each item in the WSR request
                    for (const wsrItem of findWsrItemList) {
                        // Finding the existing WSR item based on criteria
                        const findWsrItem = await WsrItem.findOne({
                            attributes: ['id', 'reference_id'],
                            where: {
                                wsr_detail_id: findWsrDetail.wsr_detail_id,
                                payment_type_category_id: wsrItem.payment_type_category_id,
                                wsr_item_status: wsr_item_status.ACTIVE,
                                reference_id: wsrItem.reference_id
                            }, raw: true
                        });

                        // If the item exists, handle the update logic
                        if (findWsrItem) {
                            if (wsrItem.reference_id) {
                                // Check if the reference IDs match
                                if (wsrItem.reference_id == findWsrItem.reference_id) {
                                    // Fetch the document category branch to ensure it's active
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: wsrItem.reference_id,
                                            branch_id: findWsrDate.branch_id,
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    });

                                    // If the branch exists, update the WSR item
                                    if (findDocumentCategoryBranch) {
                                        await WsrItem.setHeaders(req).update({
                                            wsr_amount: wsrItem.wsr_amount,
                                            wsr_item_status: wsr_item_status.ACTIVE,
                                            updated_by: findWsrDetail.created_by
                                        }, {
                                            where: {
                                                wsr_detail_id: findWsrDetail.wsr_detail_id,
                                                payment_type_category_id: wsrItem.payment_type_category_id,
                                                id: findWsrItem.id
                                            }
                                        });
                                        wsrItemIds.push(findWsrItem.id); // Collect updated item IDs
                                    }
                                } else {
                                    // If the reference IDs do not match, check the branch
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: wsrItem.reference_id,
                                            branch_id: findWsrDate.branch_id,
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    });

                                    // If the branch exists, create a new WSR item
                                    if (findDocumentCategoryBranch) {
                                        const createWsrItem = await WsrItem.setHeaders(req).create({
                                            wsr_detail_id: findWsrDetail.wsr_detail_id,
                                            payment_type_category_id: wsrItem?.payment_type_category_id,
                                            wsr_amount: wsrItem.wsr_amount,
                                            reference_id: wsrItem.reference_id,
                                            wsr_item_status: wsr_item_status.ACTIVE,
                                            created_by: findWsrDetail.created_by,
                                            updated_by: findWsrDetail.created_by
                                        } as any);
                                        if (createWsrItem) {
                                            wsrItemIds.push(createWsrItem.id); // Collect newly created item IDs
                                        }
                                    }
                                }
                            } else {
                                // If no reference ID, update the existing WSR item
                                await WsrItem.setHeaders(req).update({
                                    wsr_amount: wsrItem.wsr_amount,
                                    wsr_item_status: wsr_item_status.ACTIVE,
                                    updated_by: findWsrDetail.created_by
                                }, {
                                    where: {
                                        wsr_detail_id: findWsrDetail.wsr_detail_id,
                                        payment_type_category_id: wsrItem.payment_type_category_id,
                                        id: findWsrItem.id
                                    }
                                });
                                wsrItemIds.push(findWsrItem.id); // Collect updated item IDs
                            }
                        } else {
                            // Handle case where the item does not exist
                            if (wsrItem.reference_id) {
                                // Create a new WSR item if reference ID is present
                                const createWsrItem = await WsrItem.setHeaders(req).create({
                                    wsr_detail_id: findWsrDetail.wsr_detail_id,
                                    payment_type_category_id: wsrItem?.payment_type_category_id,
                                    wsr_amount: wsrItem.wsr_amount,
                                    reference_id: wsrItem.reference_id,
                                    wsr_item_status: wsr_item_status.ACTIVE,
                                    created_by: findWsrDetail.created_by,
                                    updated_by: findWsrDetail.created_by
                                } as any);
                                if (createWsrItem) {
                                    wsrItemIds.push(createWsrItem.id); // Collect newly created item IDs
                                }
                            } else {
                                // Create new WSR item without reference ID
                                const createWsrItem = await WsrItem.setHeaders(req).create({
                                    wsr_detail_id: findWsrDetail.wsr_detail_id,
                                    payment_type_category_id: wsrItem?.payment_type_category_id,
                                    wsr_amount: wsrItem.wsr_amount,
                                    wsr_item_status: wsr_item_status.ACTIVE,
                                    created_by: findWsrDetail.created_by,
                                    updated_by: findWsrDetail.created_by
                                } as any);
                                if (createWsrItem) {
                                    wsrItemIds.push(createWsrItem.id); // Collect newly created item IDs
                                }
                            }
                        }
                    }

                    // Update items that were not processed to inactive status
                    if (wsrItemIds.length > 0) {
                        await WsrItem.setHeaders(req).update({
                            wsr_item_status: wsr_item_status.INACTIVE
                        }, {
                            where: {
                                id: { [Op.notIn]: wsrItemIds },
                                wsr_detail_id: findWsrDetail.wsr_detail_id
                            }
                        });
                    }
                }

                // Create a notification for the user regarding the approved request
                await createNotification([findUser], req, NOTIFICATION_TYPE.INDIVIDUAL,
                    NOTIFICATIONCONSTANT.WSR_RESPONSE.content(
                        getActionUserFullName,
                        wsr_request_status.APPROVED,
                        getWeekCount(findWsrDate?.wsr_start_date, findWsrDate?.wsr_end_date),
                        moment(findWsrDate?.wsr_start_date).format("DD/MM/YYYY"),
                        moment(findWsrDate?.wsr_end_date).format("DD/MM/YYYY")
                    ),
                    NOTIFICATIONCONSTANT.WSR_RESPONSE.heading,
                    REDIRECTION_TYPE.WSR_REQUEST,
                    findWsrDate.id,
                    { wsr_id: findWsrDate.id, request_id: findWsrDetail.id }
                );
            }

            // Respond with a success message for approval
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("WSR_REQUEST_APPROVED"),
            });
        }

        // Handling rejection case
        if (request_status == wsr_request_status.REJECTED) {
            // Update the WSR request status to rejected
            const updateWsrRequest = await WsrRequest.setHeaders(req).update({
                wsr_request_status: wsr_request_status.REJECTED,
                request_remark: request_remark,
                updated_by: req.user.id
            }, { where: { id: wsr_request_id } });

            // Proceed if the update was successful
            if (updateWsrRequest.length > 0) {
                // Create a notification for the user regarding the rejected request
                await createNotification([findUser], req, NOTIFICATION_TYPE.INDIVIDUAL,
                    NOTIFICATIONCONSTANT.WSR_RESPONSE.content(
                        getActionUserFullName,
                        wsr_request_status.REJECTED,
                        getWeekCount(findWsrDate?.wsr_start_date, findWsrDate?.wsr_end_date),
                        moment(findWsrDate?.wsr_start_date).format("DD/MM/YYYY"),
                        moment(findWsrDate?.wsr_end_date).format("DD/MM/YYYY")
                    ),
                    NOTIFICATIONCONSTANT.WSR_RESPONSE.heading,
                    REDIRECTION_TYPE.WSR_REQUEST,
                    findWsrDate.id,
                    { wsr_id: findWsrDate.id, request_id: findWsrDetail.id }
                );
            }
            // Respond with a success message for rejection
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("WSR_REQUEST_REJECTED"),
            });
        }
        // Respond with an error for invalid request status
        return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: res.__("INVALID_REQUEST_STATUS"),
        });
    } catch (error) {
        // Handle errors, log them, and respond with a generic error message
        console.error(error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
        });
    }
};

const checkWsrExist = async (req: Request, res: Response) => {
    try {
        // Destructure branch_id, wsr_start_date, and wsr_end_date from the request body
        const { branch_id, wsr_start_date, wsr_end_date }: any = req.body;

        // Query the WsrDetail table to find an existing record with the specified parameters
        const getWsrDetail = await WsrDetail.findOne({
            attributes: ['id'],
            where: {
                branch_id: branch_id, // Filter by branch ID
                wsr_start_date, // Filter by start date of WSR
                wsr_end_date, // Filter by end date of WSR
                wsr_detail_status: wsr_detail_status.ACTIVE, // Ensure the WSR detail is active
            }, raw: true
        });

        // If a matching WSR detail is found, return a bad request response
        if (getWsrDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("WSR_ALREADY_ADDED"), // Message indicating WSR already exists
            });
        } else {
            // If no matching WSR detail is found, return a successful response
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FAIL_WSR_NOT_FOUND"), // Message indicating WSR not found
            });
        }
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);
        // Return a service unavailable status and an error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging
        });
    }
};

const getWsrActivity = async (req: Request, res: Response) => {
    try {
        // Destructure wsr_id from request parameters and page, size from query parameters
        const { wsr_id }: any = req.params;
        const { page, size }: any = req.query;

        // Calculate pagination parameters: limit and offset
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Fetch WSR details including associated user and branch information
        const findWsrDetail: any = await WsrDetail.findOne({
            include: [
                {
                    model: User,
                    as: "wsr_user",
                    attributes: [
                        'id',
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name", // Combine first and last name
                        ],
                        'employment_number'
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "wsr_branch",
                    attributes: [
                        "id",
                        "branch_name", // Fetch branch name
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
            ],
            where: {
                id: wsr_id, // Filter by WSR ID
                wsr_detail_status: { [Op.not]: wsr_detail_status.DELETED }, // Exclude deleted records
            },
            raw: true,
            nest: true,
        });

        // If no WSR detail is found, return an error response
        if (!findWsrDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_WSR_NOT_FOUND"), // Message indicating WSR not found
            });
        }

        // Define object to fetch activity logs related to the WSR
        const wsrDetailObj: any = {
            include: [
                {
                    model: User,
                    as: "users",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name", // Combine first and last name for activity logs
                        ],
                        "user_email", // Include user email in the results
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
            ],
            where: {
                activity_table: { [Op.in]: ['WsrItem', 'WsrRequest', 'WsrDetail'] }, // Filter activity types
                [Op.or]: [
                    { previous_data: { [Op.like]: `%"wsr_detail_id":${wsr_id}%` } }, // Match previous data
                    { new_data: { [Op.like]: `%"wsr_detail_id":${wsr_id}%` } } // Match new data
                ],
            },
            order: [['createdAt', 'DESC']], // Order by creation date descending
        };

        // Apply pagination if page and size are provided
        if (page && size) {
            wsrDetailObj.limit = Number(limit); // Set limit for results
            wsrDetailObj.offset = Number(offset); // Set offset for pagination
        }

        // Fetch activity logs based on the constructed query object
        const { count, rows: getWsrList } = await Activity.findAndCountAll(wsrDetailObj);
        const getWsrDetail = JSON.parse(JSON.stringify(getWsrList)); // Convert to a plain object for easier manipulation

        // If activity logs are found, process and return them
        if (getWsrDetail.length > 0) {
            // Format activity_table names by adding spaces before capitals
            getWsrDetail.forEach((log: any) => {
                log.activity_table = addSpacesBeforeCapitals(log.activity_table);
            });
            // Calculate total pages for pagination
            const { total_pages } = getPaginatedItems(size, page, count || 0);

            // Return successful response with WSR details and activity logs
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: {
                    id: findWsrDetail?.id,
                    wsr_branch: findWsrDetail?.wsr_branch?.branch_name,
                    wsr_user: findWsrDetail?.wsr_user?.user_full_name,
                    wsr_date: findWsrDetail.wsr_date,
                    wsr_activity: getWsrDetail,
                    wsr_start_date: findWsrDetail.wsr_start_date,
                    wsr_end_date: findWsrDetail.wsr_end_date,
                },
                page: parseInt(page),
                size: parseInt(size),
                count: count,
                total_pages,
            });
        } else {
            // If no activity logs are found, return an empty data response
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FAIL_DATA_FETCHED"), // Message indicating no data was fetched
                data: [],
                page: 0,
                size: 0,
                count: 0,
                total_pages: 0,
            });
        }
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);
        // Return a service unavailable status and an error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging
        });
    }
};

export default {
    getDsrPaymentType,
    addDsrDetail,
    updateDsrDetail,
    getDsrById,
    getDsrList,
    deleteDsrById,
    approveRejectRequest,
    getDsrRequestList,
    getDsrRequestById,
    getDsrReport,
    checkDsrExist,
    getDsrActivity,
    downloadPdfExcel,
    generateReport,
    addWsrDetail,
    updateWsrDetail,
    getWsrById,
    getWsrList,
    deleteWsrById,
    getWsrRequestById,
    getWsrRequestList,
    approveRejectRequestWsr,
    checkWsrExist,
    getWsrActivity
};