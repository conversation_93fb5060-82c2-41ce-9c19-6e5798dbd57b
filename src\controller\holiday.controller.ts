import { holiday_type_status, HolidayType } from "../models/HolidayType";
import { StatusCodes } from "http-status-codes";
import { Request, Response } from "express";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { Op } from "sequelize";
import { HolidayPolicy, holiday_policy_status } from "../models/HolidayPolicy";
import { Workbook } from "exceljs";
import moment from "moment";
import { sequelize } from "../models";
import {
  user_holiday_policy_status,
  UserHolidayPolicy,
} from "../models/UserHolidayPolicy";
import { deleteFileFromBucket, getFileFromS3 } from "../helper/upload.service";
import { Item, item_status } from "../models/Item";
import { validateModulePermission } from "../helper/common";
import { ROLE_PERMISSIONS } from "../helper/constant";

const createHolidayType = async (req: Request, res: Response) => {
  try {
    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      "holiday_management", // Holiday Management module slug
      ROLE_PERMISSIONS.CREATE,
      req?.headers?.["platform-type"]
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //   ]
    // );

    // User has permission if either check passes
    const hasPermission = checkModulePermission; // || checkAdminPermission;

    if (!hasPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const {
      holiday_type_name,
      holiday_type_description,
      has_holiday_type_default,
    } = req.body;

    const findHolidayTypeName = await HolidayType.findOne({
      where: {
        holiday_type_name,
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
        organization_id: req.user.organization_id,
      },
    });

    if (findHolidayTypeName) {
      return res.status(StatusCodes.CREATED).json({
        status: false,
        message: res.__("HOLIDAY_TYPE_NAME_ALREADY_EXIST"),
      });
    }

    const findExistingDefaultHolidayType = await HolidayType.findOne({
      where: {
        has_holiday_type_default: true,
        organization_id: req.user.organization_id,
      },
    });

    if (has_holiday_type_default && findExistingDefaultHolidayType) {
      await HolidayType.update(
        {
          has_holiday_type_default: false,
          updated_by: req.user.id,
        } as any,
        { where: { id: findExistingDefaultHolidayType.id } }
      );
    }
    const holidayType = await HolidayType.create({
      holiday_type_name,
      holiday_type_description,
      has_holiday_type_default,
      organization_id: req.user.organization_id,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);

    if (holidayType) {
      return res.status(StatusCodes.CREATED).json({
        status: true,
        message: res.__("HOLIDAY_TYPE_ADDED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_TO_ADD_HOLIDAY_TYPE"),
      });
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getHolidayType = async (req: Request, res: Response) => {
  try {
    const { holiday_type_id } = req.params;
    let getholidayType: any = await HolidayType.findOne({
      where: {
        id: holiday_type_id,
        organization_id: req.user.organization_id,
      },
    });
    getholidayType = JSON.parse(JSON.stringify(getholidayType));
    if (getholidayType) {
      getholidayType.import_file_url = `${global.config.API_BASE_URL}others/Jnext_Services_Pvt_ltd_Holiday_Information.xlsx`;
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getholidayType || {},
    });
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateHolidayType = async (req: Request, res: Response) => {
  try {
    const { holiday_type_id } = req.params;
    const {
      holiday_type_name,
      holiday_type_description,
      has_holiday_type_default,
    } = req.body;
    const getholidayType = await HolidayType.findOne({
      where: {
        id: holiday_type_id,
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
      },
    });

    const findHolidayTypeName: any = await HolidayType.findOne({
      where: {
        holiday_type_name,
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
        organization_id: req.user.organization_id,
      },
    });

    if (findHolidayTypeName && findHolidayTypeName.id != holiday_type_id) {
      return res.status(StatusCodes.CREATED).json({
        status: false,
        message: res.__("HOLIDAY_TYPE_NAME_ALREADY_EXIST"),
      });
    }

    if (!getholidayType) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("HOLIDAY_TYPE_NOT_FOUND") });
    }

    const findExistingDefaultHolidayType = await HolidayType.findOne({
      where: {
        has_holiday_type_default: true,
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
        organization_id: req.user.organization_id,
      },
    });

    if (
      has_holiday_type_default &&
      findExistingDefaultHolidayType &&
      findExistingDefaultHolidayType.id !== getholidayType.id
    ) {
      await HolidayType.update(
        {
          has_holiday_type_default: false,
          updated_by: req.user.id,
        } as any,
        { where: { id: findExistingDefaultHolidayType.id } }
      );
    }
    const updateHolidayType = await HolidayType.update(
      {
        holiday_type_name,
        holiday_type_description,
        has_holiday_type_default,
        updated_by: req.user.id,
      },
      { where: { id: getholidayType.id } }
    );

    if (updateHolidayType.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("HOLIDAY_TYPE_UPDATED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_TO_UPDATE_HOLIDAY_TYPE"),
      });
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const deleteHolidayType = async (req: Request, res: Response) => {
  try {
    const { holiday_type_id } = req.params;
    const getHolidayType = await HolidayType.findOne({
      where: {
        id: holiday_type_id,
      },
    });
    if (!getHolidayType) {
      return res
        .status(404)
        .json({ message: res.__("HOLIDAY_TYPE_NOT_FOUND") });
    }
    const detroyHoliday = await HolidayType.update(
      {
        holiday_type_status: holiday_type_status.DELETED,
        updated_by: req.user.id,
      },
      { where: { id: getHolidayType.id } }
    );

    if (detroyHoliday) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("HOLIDAY_TYPE_DELETED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("HOLIDAY_TYPE_DELETED_SUCCESSFULLY"),
      });
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const inactiveHolidayType = async (req: Request, res: Response) => {
  try {
    const { holiday_type_id, holidayTypeStatus } = req.body;
    const getHolidayType = await HolidayType.findOne({
      where: {
        id: holiday_type_id,
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
        organization_id: req.user.organization_id,
      },
    });
    if (!getHolidayType) {
      return res
        .status(404)
        .json({ message: res.__("HOLIDAY_TYPE_NOT_FOUND") });
    }
    const updateHolidayStatus = await HolidayType.update(
      { holiday_type_status: holidayTypeStatus, updated_by: req.user.id },
      { where: { id: getHolidayType.id } }
    );

    if (updateHolidayStatus) {
      if (holidayTypeStatus == holiday_type_status.INACTIVE) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("HOLIDAY_TYPE_INACTIVE_SUCCESSFULLY"),
        });
      }
      if (holidayTypeStatus == holiday_type_status.ACTIVE) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("HOLIDAY_TYPE_ACTIVE_SUCCESSFULLY"),
        });
      }
    } else {
      if (holidayTypeStatus == holiday_type_status.INACTIVE) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("FAIL_TO_INACTIVE_HOLIDAY_TYPE"),
        });
      }
      if (holidayTypeStatus == holiday_type_status.ACTIVE) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("FAIL_TO_ACTIVE_HOLIDAY_TYPE"),
        });
      }
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getHolidayTypeList = async (req: Request, res: Response) => {
  try {
    const { page, size, search, holidayTypeStatus, holiday_policy_year }: any =
      req.query;
    const { limit, offset } = getPagination(page, size);

    const whereObj: any = {
      holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
      organization_id: req.user.organization_id,
    };
    const holidayPolicyWhere: any = {
      holiday_policy_status: holiday_policy_status.ACTIVE,
    };

    // Apply filters based on query params
    if (holidayTypeStatus) {
      whereObj.holiday_type_status = holidayTypeStatus;
    }

    // Modify search logic to work for both Leave Types & Leave Policies
    if (search) {
      whereObj[Op.or] = [
        { holiday_type_name: { [Op.like]: `%${search}%` } },
        { "$holiday_type.holiday_policy_name$": { [Op.like]: `%${search}%` } },
      ];
    }

    // If holiday_policy_year is provided, filter based on date range
    if (holiday_policy_year) {
      const startOfYear = moment(`${holiday_policy_year}-01-01`)
        .startOf("day")
        .toDate();
      const endOfYear = moment(`${holiday_policy_year}-12-31`)
        .endOf("day")
        .toDate();

      whereObj[Op.or] = [
        {
          "$holiday_type.holiday_policy_start_date$": {
            [Op.gte]: startOfYear,
            [Op.lte]: endOfYear,
          },
        },
        {
          "$holiday_type.holiday_policy_end_date$": {
            [Op.gte]: startOfYear,
            [Op.lte]: endOfYear,
          },
        },
      ];
    }

    // Query configuration
    const getHolidayTypeObj: any = {
      distinct: true, // Ensures unique results
      where: whereObj,
      include: [
        {
          model: HolidayPolicy,
          as: "holiday_type",
          where: holidayPolicyWhere,
          attributes: [
            "id",
            "has_leave_reprocess",
            "holiday_policy_name",
            "holiday_policy_colour",
            "holiday_policy_description",
            "holiday_policy_start_date",
            "holiday_policy_end_date",
            "holiday_policy_status",
            "createdAt",
            "updatedAt",
            [
              sequelize.literal(`(
                        SELECT COUNT(*)
                        FROM nv_user_holiday_policy AS uhp
                        INNER JOIN nv_users AS u ON uhp.user_id = u.id
                        WHERE uhp.holiday_policy_id = holiday_type.id
                        AND uhp.user_holiday_policy_status = '${user_holiday_policy_status.ACTIVE}'
                         AND u.user_status NOT IN ('deleted', 'cancelled')
                    )`),
              "holiday_policy_user_count",
            ],
            "holiday_type_id",
            [
              sequelize.literal(`(
                        SELECT GROUP_CONCAT(uhp.user_id)
                        FROM nv_user_holiday_policy AS uhp
                        INNER JOIN nv_users AS u ON uhp.user_id = u.id
                        WHERE uhp.holiday_policy_id = holiday_type.id 
                        AND uhp.user_holiday_policy_status = '${user_holiday_policy_status.ACTIVE}'
                        AND u.user_status NOT IN ('deleted', 'cancelled')
                    )`),
              "user_policy_ids",
            ],
          ],
          required: false,
        },
      ],
      order: [["has_holiday_type_default", "DESC"]],
      raw: false,
      nest: true,
    };

    // Apply pagination only if page & size are provided
    if (page && size) {
      getHolidayTypeObj.limit = Number(limit);
      getHolidayTypeObj.offset = Number(offset);
    }

    // Fetch Holiday Types with associated policies
    const getHolidayTypeList = await HolidayType.findAll(getHolidayTypeObj);

    // Fetch count for pagination
    const count = await HolidayType.count({
      where: whereObj,
      include: [
        {
          model: HolidayPolicy,
          as: "holiday_type",
          where: holidayPolicyWhere,
          required: false,
        },
      ],
      distinct: true,
    });

    // Calculate total pages
    const { total_pages } = getPaginatedItems(size, page, count || 0);

    return res.status(StatusCodes.OK).json({
      status: true,
      data: getHolidayTypeList,
      message: res.__("SUCCESS_FETCHED"),
      upload_link: `${global.config.API_BASE_URL}others/Namastevillage_Teamtrainhub_Holiday_Information.xlsx`,
      count: count || 0,
      page: parseInt(page) || 1,
      size: parseInt(size) || 10,
      total_pages,
    });
  } catch (error: any) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const createHolidayPolicy = async (req: Request, res: Response) => {
  try {
    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      "holiday_management", // Holiday Management module slug
      ROLE_PERMISSIONS.CREATE
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //   ]
    // );

    // User has permission if either check passes
    const hasPermission = checkModulePermission; // || checkAdminPermission;

    if (!hasPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const { holiday_type_id } = req.params;
    const {
      holiday_policy_name,
      holiday_policy_colour,
      holiday_policy_description,
      holiday_policy_start_date,
      holiday_policy_end_date,
      has_leave_reprocess,
    } = req.body;

    const getHolidayType = await HolidayType.findOne({
      where: {
        id: Number(holiday_type_id),
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
      },
    });
    if (!getHolidayType) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("HOLIDAY_TYPE_NOT_FOUND") });
    }

    const findHolidayPolicyByName = await HolidayPolicy.findOne({
      where: {
        holiday_policy_name: holiday_policy_name,
        holiday_type_id: holiday_type_id,
        holiday_policy_status: holiday_policy_status.ACTIVE,
      },
    });

    if (findHolidayPolicyByName) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("HOLIDAY_POLICY_NAME_ALREADY_EXIST"),
      });
    }

    // // Check if there's an overlapping holiday type with the same name
    // const findOverlappingHoliday = await HolidayPolicy.findOne({
    //     where: {
    //         holiday_policy_name,
    //         holiday_type_id: holiday_type_id,
    //         holiday_policy_start_date: { [Op.lte]: holiday_policy_end_date }, // start_date <= end_date
    //         holiday_policy_end_date: { [Op.gte]: holiday_policy_start_date }, // end_date >= start_date
    //     },
    // });

    // if (findOverlappingHoliday) {
    //     return res.status(StatusCodes.CONFLICT).json({
    //         status: false,
    //         message: res.__("HOLIDAY_TYPE_DATE_RANGE_CONFLICT"),
    //     });
    // }

    const addHolidayPolicy = await HolidayPolicy.create({
      holiday_policy_name,
      holiday_policy_colour,
      holiday_policy_description,
      holiday_policy_start_date,
      holiday_policy_end_date,
      has_leave_reprocess,
      created_by: req.user.id,
      updated_by: req.user.id,
      holiday_type_id: holiday_type_id,
    } as any);

    if (addHolidayPolicy) {
      return res.status(StatusCodes.CREATED).json({
        status: true,
        message: res.__("SUCCESS_HOLIDAY_CREATED"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_TO_ADD_HOLIDAY_POLICY"),
      });
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const updateHolidayPolicy = async (req: Request, res: Response) => {
  try {
    const { holiday_policy_id } = req.params;
    const {
      holiday_policy_name,
      holiday_policy_colour,
      holiday_policy_description,
      holiday_policy_start_date,
      holiday_policy_end_date,
      has_leave_reprocess,
      holiday_type_id,
    } = req.body;

    const getHolidayPolicy = await HolidayPolicy.findOne({
      where: { id: holiday_policy_id },
    });

    if (!getHolidayPolicy) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("HOLIDAY_POLICY_NOT_FOUND") });
    }

    const findHolidayPolicyByName: any = await HolidayPolicy.findOne({
      where: {
        holiday_policy_name: holiday_policy_name,
        holiday_type_id: holiday_type_id,
        holiday_policy_status: holiday_policy_status.ACTIVE,
      },
    });

    if (
      findHolidayPolicyByName &&
      findHolidayPolicyByName?.id != holiday_policy_id
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("HOLIDAY_POLICY_NAME_ALREADY_EXIST"),
      });
    }

    // // Check if there's an overlapping holiday policy for the same holiday_type_id
    // const findOverlappingPolicy = await HolidayPolicy.findOne({
    //     where: {
    //         holiday_type_id,
    //         id: { [Op.ne]: holiday_policy_id }, // Exclude the current policy
    //         holiday_policy_start_date: { [Op.lte]: holiday_policy_end_date }, // start_date <= end_date
    //         holiday_policy_end_date: { [Op.gte]: holiday_policy_start_date }, // end_date >= start_date
    //     }
    // });

    // if (findOverlappingPolicy) {
    //     return res.status(StatusCodes.CONFLICT).json({
    //         status: false,
    //         message: res.__("HOLIDAY_TYPE_DATE_RANGE_CONFLICT"),
    //     });
    // }

    const updateHolidayPolicy = await HolidayPolicy.update(
      {
        holiday_policy_name,
        holiday_policy_colour,
        holiday_policy_description,
        holiday_policy_start_date,
        holiday_policy_end_date,
        has_leave_reprocess,
        holiday_type_id,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any,
      { where: { id: getHolidayPolicy.id } }
    );

    if (updateHolidayPolicy.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("HOLIDAY_POLICY_UPDATED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_TO_UPDATE_HOLIDAY_POLICY"),
      });
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getHolidayPolicy = async (req: Request, res: Response) => {
  try {
    const { holiday_policy_id } = req.params;
    const getHolidayPolicy = await HolidayPolicy.findOne({
      where: {
        id: holiday_policy_id,
      },
    });
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getHolidayPolicy || {},
    });
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const removeHolidayPolicy = async (req: Request, res: Response) => {
  try {
    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      "holiday_management", // Holiday Management module slug
      ROLE_PERMISSIONS.DELETE
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //   ]
    // );

    // User has permission if either check passes
    const hasPermission = checkModulePermission; // || checkAdminPermission;

    if (!hasPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const { holiday_policy_id } = req.params;
    const getHolidayPolicy = await HolidayPolicy.findOne({
      where: {
        id: holiday_policy_id,
      },
    });

    if (!getHolidayPolicy) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("HOLIDAY_POLICY_NOT_FOUND"),
      });
    }

    const findUserHolidayPolicy = await UserHolidayPolicy.findAll({
      where: {
        holiday_policy_id: holiday_policy_id,
        user_holiday_policy_status: user_holiday_policy_status.ACTIVE,
      },
    });
    if (findUserHolidayPolicy.length > 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("SOME_USER_HAVE_ALREADY_ASSIGNED_HOLIDAY_POLICY"),
      });
    }
    const removeHolidayPolicy = await HolidayPolicy.update(
      { holiday_policy_status: holiday_policy_status.DELETED },
      { where: { id: holiday_policy_id } }
    );

    if (removeHolidayPolicy.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("HOLIDAY_POLICY_REMOVED_SUCCESSFULLY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_TO_REMOVE_HOLIDAY_POLICY"),
      });
    }
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const importHolidayPolicy = async (req: any, res: Response) => {
  try {
    const { holiday_type_id } = req.params;
    const filePath = req?.files[0]?.path;
    const workbook = new Workbook();

    const findItem = await Item.findOne({
      where: {
        id: req.files[0].item_id,
      },
    });

    if (!findItem) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FILE_NOT_FOUND"),
      });
    }

    const fileBuffer: any = await getFileFromS3(findItem?.item_location);
    if (!fileBuffer.success) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FILE_NOT_FOUND"),
      });
    }
    await workbook.xlsx.load(fileBuffer.buffer);
    const worksheet = workbook.getWorksheet("Holiday");

    if (!worksheet) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("WORKSHEET_HOLIDAY_NOT_FOUND"),
      });
    }

    const holidayData: any = [];
    const errors: any = [];
    worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
      if (rowNumber > 1) {
        // Skipping the header row
        const holidayName = row.getCell(1).value;
        const startDate: any = row.getCell(2).value;
        const endDate: any = row.getCell(3).value;

        // Validate if dates exist
        if (!holidayName || !startDate || !endDate) {
          errors.push({
            row: rowNumber,
            error: "Holiday name, start date, and end date are required.",
          });
          return;
        }

        // Validate date format
        const parsedStartDate = moment(startDate, "YYYY-MM-DD", true);
        const parsedEndDate = moment(endDate, "YYYY-MM-DD", true);

        if (!parsedStartDate.isValid() || !parsedEndDate.isValid()) {
          errors.push({
            row: rowNumber,
            error: "Invalid date format. Use YYYY-MM-DD.",
          });
          return;
        }

        // Validate start date is before or equal to end date
        if (parsedStartDate.isAfter(parsedEndDate)) {
          errors.push({
            row: rowNumber,
            error: "Start date cannot be after end date.",
          });
          return;
        }

        holidayData.push({
          name: holidayName,
          start_date: moment(startDate)
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss"),
          end_date: moment(endDate)
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss"),
        });
      }
    });

    const alreadyExistHolidays = [];
    if (errors.length > 0) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("VALIDATION_ERRORS_FOUND"),
        errors,
      });
    }
    for (const holiday of holidayData) {
      const findHoliday = await HolidayPolicy.findOne({
        where: {
          holiday_policy_name: holiday.name.trim(),
          holiday_policy_start_date: moment(holiday.start_date)
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss"),
          holiday_policy_end_date: moment(holiday.end_date)
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss"),
          holiday_policy_status: holiday_policy_status.ACTIVE,
          holiday_type_id: Number(holiday_type_id),
        },
      });

      if (!findHoliday) {
        await HolidayPolicy.create({
          holiday_policy_name: holiday.name,
          holiday_policy_start_date: holiday.start_date,
          holiday_policy_end_date: holiday.end_date,
          holiday_policy_status: holiday_policy_status.ACTIVE,
          holiday_type_id,
          created_by: 1,
          updated_by: 1,
        } as any);
      } else {
        alreadyExistHolidays.push({
          id: findHoliday.id,
          name: findHoliday.holiday_policy_name,
          holiday_policy_start_date: findHoliday.holiday_policy_start_date,
        });
      }
    }

    if (fileBuffer.success) {
      await deleteFileFromBucket(process.env.NODE_ENV!, filePath);
      await Item.update(
        { item_status: item_status.DELETED },
        { where: { id: req?.files[0]?.item_id } }
      );
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("HOLIDAY_DATA_IMPORTED_SUCCESSFULLY"),
      duplicates: alreadyExistHolidays,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getUserHolidayPolicy = async (req: Request, res: Response) => {
  try {
    const {
      search,
      holiday_policy_year,
      user_id,
      start_date,
      end_date,
      up_comming,
    }: any = req.query;
    const findOrgHolidayType = await HolidayType.findAll({
      where: {
        organization_id: req.user.organization_id,
        holiday_type_status: { [Op.not]: holiday_type_status.DELETED },
      },
    });
    const userId = user_id || req.user.id;
    const whereObj: any = {
      holiday_policy_status: holiday_policy_status.ACTIVE,
      id: {
        [Op.in]: sequelize.literal(`(
          SELECT holiday_policy_id 
          FROM nv_user_holiday_policy 
          WHERE user_id = ${userId} 
          AND user_holiday_policy_status = '${user_holiday_policy_status.ACTIVE}'
        )`),
      },
    };

    if (findOrgHolidayType.length > 0) {
      whereObj.holiday_type_id = {
        [Op.in]: findOrgHolidayType.map((item: any) => item.id),
      };
    }

    if (search) {
      whereObj.holiday_policy_name = { [Op.like]: `%${search}%` };
    }

    // If a holiday_policy_year is provided, filter using joined HolidayPolicy dates.
    if (holiday_policy_year) {
      const startOfYear = moment(`${holiday_policy_year}-01-01`)
        .startOf("day")
        .toDate();
      const endOfYear = moment(`${holiday_policy_year}-12-31`)
        .endOf("day")
        .toDate();

      // Use the $ alias syntax to reference HolidayPolicy fields (alias is "holiday_type")
      whereObj[Op.or] = [
        {
          holiday_policy_start_date: {
            [Op.gte]: startOfYear,
            [Op.lte]: endOfYear,
          },
        },
        {
          holiday_policy_end_date: {
            [Op.gte]: startOfYear,
            [Op.lte]: endOfYear,
          },
        },
      ];
    }

    // If start_date and end_date are provided, filter policies that fall within the range
    if (start_date && end_date) {
      const formattedStartDate = moment(start_date).startOf("day").toDate();
      const formattedEndDate = moment(end_date).endOf("day").toDate();

      // Override year-based filter if specific dates are provided
      whereObj[Op.or] = [
        {
          // Policies where start date falls within the range
          holiday_policy_start_date: {
            [Op.gte]: formattedStartDate,
            [Op.lte]: formattedEndDate,
          },
        },
        {
          // Policies where end date falls within the range
          holiday_policy_end_date: {
            [Op.gte]: formattedStartDate,
            [Op.lte]: formattedEndDate,
          },
        },
        {
          // Policies that span the entire range
          holiday_policy_start_date: { [Op.lte]: formattedStartDate },
          holiday_policy_end_date: { [Op.gte]: formattedEndDate },
        },
      ];
    }

    // If up_comming is true, filter to show ONLY upcoming holidays (no previous ones)
    if (up_comming === "true" || up_comming === true) {
      const today = moment().startOf("day").toDate();

      // This ensures NO previous holidays are returned, only today and future
      whereObj.holiday_policy_start_date = {
        [Op.gte]: today, // Only holidays that start today or in the future
      };
    }

    const getHolidayTypeObj: any = {
      where: whereObj,
      attributes: [
        "id",
        "has_leave_reprocess",
        "holiday_policy_name",
        "holiday_policy_colour",
        "holiday_policy_description",
        "holiday_policy_start_date",
        "holiday_policy_end_date",
        "holiday_policy_status",
        "createdAt",
        "updatedAt",
      ],
      order: [["holiday_policy_start_date", "ASC"]],
      raw: false,
      nest: true,
    };
    const getHolidayTypeList = await HolidayPolicy.findAll(getHolidayTypeObj);

    return res.status(StatusCodes.OK).json({
      status: true,
      data: getHolidayTypeList,
      message: res.__("SUCCESS_FETCHED"),
    });
  } catch (error: any) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  createHolidayType,
  getHolidayType,
  updateHolidayType,
  deleteHolidayType,
  getHolidayTypeList,
  createHolidayPolicy,
  updateHolidayPolicy,
  getHolidayPolicy,
  removeHolidayPolicy,
  importHolidayPolicy,
  inactiveHolidayType,
  getUserHolidayPolicy,
};
