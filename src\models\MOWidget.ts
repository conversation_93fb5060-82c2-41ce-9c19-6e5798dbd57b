"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface widgetAttributes {
  id: number;
  index: number;
  name: string;
  widget: string;
  type: string;
  subType: string;
  filters: any;
  slug: string;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum widget_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export const widget_type = {
  CHART: "chart",
  NUMBER: "number",
  PERCENTAGE: "percentage",
  TABLE: "table",
  LIST: "list",
  CARD: "card",
  METRIC: "metric",
} as const;

export const widget_sub_type = {
  PIE: "pie",
  LINE: "line",
  BAR: "bar",
  DONUT: "donut",
  AREA: "area",
  COLUMN: "column",
  GAUGE: "gauge",
  COUNTER: "counter",
  PROGRESS: "progress",
  SIMPLE_TABLE: "simple_table",
  DATA_TABLE: "data_table",
  BULLET_LIST: "bullet_list",
  NUMBERED_LIST: "numbered_list",
  INFO_CARD: "info_card",
  STAT_CARD: "stat_card",
  KPI: "kpi",
  SUMMARY: "summary",
} as const;

export class MOWidget
  extends Model<widgetAttributes, never>
  implements widgetAttributes
{
  id!: number;
  index!: number;
  name!: string;
  widget!: string;
  type!: string;
  subType!: string;
  filters!: any;
  slug!: string;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }

  // Auto-generate index based on organization_id
  public static async generateIndex(organization_id: string): Promise<number> {
    const lastWidget = await MOWidget.findOne({
      where: { organization_id },
      order: [['index', 'DESC']],
      attributes: ['index']
    });

    return lastWidget ? lastWidget.index + 1 : 1;
  }
}

MOWidget.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    index: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    widget: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    subType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    filters: {
      type: DataTypes.TEXT,
      allowNull: true,
      defaultValue: null
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        name: 'unique_widget_slug_per_org',
        msg: 'Widget slug must be unique within organization'
      },
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize: sequelize,
    tableName: "mo_widgets",
    modelName: "MOWidget",
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['slug', 'organization_id'],
        name: 'unique_widget_slug_per_org'
      },
      {
        fields: ['organization_id'],
        name: 'idx_widget_organization'
      },
      {
        fields: ['type'],
        name: 'idx_widget_type'
      },
      {
        fields: ['created_by'],
        name: 'idx_widget_created_by'
      }
    ]
  }
);

// Auto-generate index before creating a widget
MOWidget.addHook("beforeCreate", async (widget: any) => {
  if (!widget.index && widget.organization_id) {
    widget.index = await MOWidget.generateIndex(widget.organization_id);
  }
});

// Add hooks for activity logging
MOWidget.addHook("afterCreate", async (widget: MOWidget) => {
  await addActivity("MOWidget", "created", widget);
});

MOWidget.addHook("afterUpdate", async (widget: any) => {
  await addActivity("MOWidget", "updated", widget);
});

MOWidget.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

MOWidget.addHook("afterDestroy", async (widget: MOWidget) => {
  await addActivity("MOWidget", "deleted", widget);
});

export default MOWidget;
